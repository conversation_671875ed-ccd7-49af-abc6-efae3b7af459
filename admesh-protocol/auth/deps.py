from fastapi import Request, HTTPException, Depends
from firebase_admin import auth as firebase_auth
from typing import Optional
import logging

logger = logging.getLogger(__name__)

def verify_firebase_token(request: Request):
    """
    Verify Firebase ID token from Authorization header.
    Supports both email/password and Google sign-in tokens.
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        logger.warning("Missing or invalid Authorization header")
        raise HTTPException(
            status_code=401,
            detail="Missing or invalid Firebase token. Please include 'Authorization: Bearer <token>' header."
        )

    token = auth_header.replace("Bearer ", "")

    if not token.strip():
        logger.warning("Empty token provided")
        raise HTTPException(
            status_code=401,
            detail="Empty Firebase token provided"
        )

    try:
        # Verify the Firebase ID token
        decoded_token = firebase_auth.verify_id_token(token)
        logger.info(f"Token verified successfully for user: {decoded_token.get('uid')}")
        logger.debug(f"Token claims: {decoded_token}")
        return decoded_token
    except firebase_auth.InvalidIdTokenError as e:
        logger.error(f"Invalid Firebase ID token: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail=f"Invalid Firebase ID token: {str(e)}"
        )
    except firebase_auth.ExpiredIdTokenError as e:
        logger.error(f"Expired Firebase ID token: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail="Firebase token has expired. Please sign in again."
        )
    except firebase_auth.RevokedIdTokenError as e:
        logger.error(f"Revoked Firebase ID token: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail="Firebase token has been revoked. Please sign in again."
        )
    except firebase_auth.CertificateFetchError as e:
        logger.error(f"Certificate fetch error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Authentication service temporarily unavailable. Please try again."
        )
    except Exception as e:
        logger.error(f"Unexpected error verifying Firebase token: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail="Failed to verify Firebase token. Please sign in again."
        )

def require_role(required_role: str):
    """
    Dependency that requires a specific role for access.
    """
    def dependency(decoded_token = Depends(verify_firebase_token)):
        user_role = decoded_token.get("role")
        user_uid = decoded_token.get("uid")

        if user_role != required_role:
            logger.warning(f"Access denied for user {user_uid}: required role '{required_role}', but user has role '{user_role}'")
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. {required_role.title()} role required."
            )

        logger.info(f"Role verification successful for user {user_uid} with role '{user_role}'")
        return decoded_token
    return dependency

async def optional_verify_token(request: Request) -> Optional[dict]:
    """
    Optional token verification that returns None instead of raising exceptions.
    Useful for endpoints that work with or without authentication.
    """
    auth_header = request.headers.get("Authorization")
    logger.debug(f"Auth header present: {bool(auth_header)}")

    if auth_header and auth_header.startswith("Bearer "):
        try:
            token = auth_header.replace("Bearer ", "")
            if not token.strip():
                logger.debug("Empty token provided in optional verification")
                return None

            logger.debug(f"Token length: {len(token)}")
            decoded_token = firebase_auth.verify_id_token(token)
            logger.info(f"Optional token verified successfully for user: {decoded_token.get('uid')}")
            return decoded_token
        except firebase_auth.InvalidIdTokenError as e:
            logger.debug(f"Invalid token in optional verification: {str(e)}")
            return None
        except firebase_auth.ExpiredIdTokenError as e:
            logger.debug(f"Expired token in optional verification: {str(e)}")
            return None
        except firebase_auth.RevokedIdTokenError as e:
            logger.debug(f"Revoked token in optional verification: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in optional token verification: {str(e)}")
            return None
    else:
        logger.debug("No valid Authorization header found in optional verification")
    return None