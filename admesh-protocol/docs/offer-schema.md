# Ad<PERSON><PERSON> Offer Schema (`admesh.offer.json`)

This document defines the canonical offer schema used across the AdMesh Protocol.
It ensures consistency, trust, and structure for brands, agents, and extensions.

---

## 🧱 JSON Schema Fields

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "title": "Ad<PERSON>esh Offer",
  "type": "object",
  "required": [
    "id", "offer_title", "offer_description", "url", "suggestion_reason",
    "reward_note", "payout", "categories", "trust_score",
    "budget", "brand_id", "created_at"
  ],
  "properties": {
    "id": { "type": "string", "description": "UUID of the offer" },
    "offer_title": { "type": "string", "description": "Short offer headline" },
    "offer_description": { "type": "string", "description": "Expanded details of the offer" },
    "url": { "type": "string", "format": "uri", "description": "Target landing page URL" },
    "suggestion_reason": { "type": "string", "description": "Why this offer is relevant to the user intent" },
    "reward_note": { "type": "string", "description": "What the user/agent earns (e.g. cashback, bounty)" },
    "payout": {
      "type": "object",
      "properties": {
        "amount": { "type": "number" },
        "currency": { "type": "string", "default": "USD" },
        "model": { "type": "string", "enum": ["CPA", "CPC", "Recurring"] }
      },
      "required": ["amount", "model"]
    },
    "categories": {
      "type": "array",
      "items": { "type": "string" }
    },
    "trust_score": { "type": "number", "minimum": 0, "maximum": 100 },
    "budget": { "type": "number", "description": "Total budget for offer availability" },
    "brand_id": { "type": "string", "description": "Unique brand identifier (Firebase UID or UUID)" },
    "created_at": { "type": "string", "format": "date-time" },
    "valid_until": { "type": "string", "format": "date-time" },
    "tracking": {
      "type": "object",
      "properties": {
        "click_url": { "type": "string", "format": "uri" },
        "conversion_url": { "type": "string", "format": "uri" },
        "pixel": { "type": "string", "format": "uri" }
      }
    },
    "keywords": {
      "type": "array",
      "items": { "type": "string" },
      "description": "Agent-optimizable labels like 'popular', 'eco-friendly'"
    },
    "meta": {
      "type": "object",
      "additionalProperties": true
    }
  }
}
```

---

## 💡 Notes
- `payout.model` supports `CPA`, `CPC`, and `Recurring` (future-ready)
- `trust_score` is computed and updated by the protocol engine
- `meta` allows brands to pass extra details for agent customization

---

## 📘 See Also
- [`quickstart.md`](./quickstart.md)
- [`endpoints.md`](./endpoints.md)
- [`earnings-model.md`](./earnings-model.md)
