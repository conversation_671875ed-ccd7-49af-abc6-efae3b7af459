"""
Custom CORS middleware that supports wildcard patterns for origins
"""
import re
from typing import List, Optional, Union
from fastapi import Request, Response
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import PlainTextResponse
import logging

logger = logging.getLogger(__name__)


class CustomCORSMiddleware(BaseHTTPMiddleware):
    """
    Custom CORS middleware that supports wildcard patterns in origins
    """
    
    def __init__(
        self,
        app,
        allow_origins: List[str] = None,
        allow_origin_regex: str = None,
        allow_methods: List[str] = None,
        allow_headers: List[str] = None,
        allow_credentials: bool = False,
        expose_headers: List[str] = None,
        max_age: int = 600,
    ):
        super().__init__(app)
        self.allow_origins = allow_origins or []
        self.allow_origin_regex = allow_origin_regex
        self.allow_methods = allow_methods or ["GET"]
        self.allow_headers = allow_headers or []
        self.allow_credentials = allow_credentials
        self.expose_headers = expose_headers or []
        self.max_age = max_age
        
        # Compile regex patterns for wildcard origins
        self.origin_patterns = []
        for origin in self.allow_origins:
            if "*" in origin:
                # Convert wildcard pattern to regex
                pattern = origin.replace("*", "([a-zA-Z0-9-]+)")
                pattern = pattern.replace(".", r"\.")
                pattern = f"^{pattern}$"
                self.origin_patterns.append(re.compile(pattern))
            else:
                # Exact match patterns
                self.origin_patterns.append(origin)

    def is_allowed_origin(self, origin: str) -> bool:
        """Check if the origin is allowed"""
        logger.info(f"🔍 Checking origin: {origin}")
        logger.info(f"🔍 Allowed origins: {self.allow_origins}")

        if not origin:
            logger.info("❌ No origin provided")
            return False

        # Check for wildcard match
        if "*" in self.allow_origins:
            logger.info("✅ Wildcard (*) found in allowed origins")
            return True

        # Check exact matches and regex patterns
        for pattern in self.origin_patterns:
            if isinstance(pattern, str):
                # Exact match
                if origin == pattern:
                    logger.info(f"✅ Exact match found: {pattern}")
                    return True
            else:
                # Regex pattern
                if pattern.match(origin):
                    logger.info(f"✅ Regex match found: {pattern.pattern}")
                    return True

        # Check if origin matches useadmesh.com pattern (only for production)
        if (origin.endswith(".useadmesh.com") or origin == "https://useadmesh.com") and \
           any("useadmesh.com" in allowed_origin for allowed_origin in self.allow_origins):
            logger.info("✅ useadmesh.com subdomain allowed")
            return True

        logger.info("❌ Origin not allowed")
        return False

    async def dispatch(self, request: Request, call_next):
        origin = request.headers.get("origin")
        
        # Handle preflight requests
        if request.method == "OPTIONS":
            if origin and self.is_allowed_origin(origin):
                response = PlainTextResponse("OK", status_code=200)
                response.headers["Access-Control-Allow-Origin"] = origin
                response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
                response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
                if self.allow_credentials:
                    response.headers["Access-Control-Allow-Credentials"] = "true"
                response.headers["Access-Control-Max-Age"] = str(self.max_age)
                return response
            else:
                # Origin not allowed
                return PlainTextResponse("CORS preflight failed", status_code=403)
        
        # Handle actual requests
        response = await call_next(request)
        
        if origin and self.is_allowed_origin(origin):
            response.headers["Access-Control-Allow-Origin"] = origin
            if self.allow_credentials:
                response.headers["Access-Control-Allow-Credentials"] = "true"
            if self.expose_headers:
                response.headers["Access-Control-Expose-Headers"] = ", ".join(self.expose_headers)
        
        return response


def get_custom_cors_middleware(
    allow_origins: List[str],
    allow_credentials: bool = True,
    allow_methods: List[str] = None,
    allow_headers: List[str] = None,
):
    """
    Factory function to create custom CORS middleware with default settings
    """
    if allow_methods is None:
        allow_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
    
    if allow_headers is None:
        allow_headers = [
            "Accept",
            "Accept-Language",
            "Content-Language",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "X-CSRF-Token",
            "Cache-Control",
            "Pragma",
        ]
    
    return CustomCORSMiddleware(
        app=None,  # Will be set by FastAPI
        allow_origins=allow_origins,
        allow_credentials=allow_credentials,
        allow_methods=allow_methods,
        allow_headers=allow_headers,
    )
