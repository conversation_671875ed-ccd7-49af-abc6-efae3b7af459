"""
GEO Check API endpoints for analyzing brand visibility in AI-powered search engines.
Generative Engine Optimization (GEO) focuses on optimizing content for AI models
rather than traditional search engines.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, HttpUrl
from typing import List, Dict, Any, Optional, Literal
import asyncio
import aiohttp
import re
from bs4 import BeautifulSoup
import logging
from datetime import datetime
import random
import os
import json
import requests
from urllib.parse import urljoin, urlparse
import xml.etree.ElementTree as ET

from auth.deps import require_role
from auth.firebase_auth import get_current_user
from firebase.config import get_db
from google.cloud import firestore

logger = logging.getLogger(__name__)

# Constants for GEO report pricing
GEO_REPORT_COST_CENTS = 900  # $9.00 in cents
FREE_REPORTS_LIMIT = 2

router = APIRouter(prefix="/geo", tags=["geo-check"])

class GEOCheckRequest(BaseModel):
    brand_id: Optional[str] = None  # If not provided, use authenticated user's brand

class GEORecommendation(BaseModel):
    priority: Literal["high", "medium", "low"]
    category: str
    title: str
    description: str
    impact: str

class PageAnalysis(BaseModel):
    url: str
    title: str
    summary: str
    score: int

class GEOAnalysisResponse(BaseModel):
    overallScore: int
    promptMentionRate: float  # 40% weight
    citationRate: float       # 20% weight
    websiteOptimization: int  # 30% weight
    sentimentTone: int        # 10% weight
    aiDiscoverability: Dict[str, Any]
    contentOptimization: Dict[str, Any]
    competitiveAnalysis: Dict[str, Any]
    analyzedPages: List[PageAnalysis]
    simulatedQueries: List[Dict[str, Any]]
    recommendations: List[GEORecommendation]
    analyzedAt: datetime
    brandId: str

class GEOAnalyzer:
    """Core GEO analysis engine"""

    def __init__(self):
        self.ai_queries = [
            "What are the best {industry} companies?",
            "Who are the top {industry} providers?",
            "Which {industry} solution should I choose?",
            "Best {industry} tools for small business",
            "Most reliable {industry} companies",
            "Leading {industry} platforms",
            "Top-rated {industry} services",
            "Best {industry} software for startups"
        ]
        self.db = get_db()

    def call_openrouter(self, prompt: str, model: str = "anthropic/claude-3-sonnet") -> str:
        """Call OpenRouter API with Claude/GPT-4"""
        headers = {
            "Authorization": f"Bearer {os.getenv('OPENROUTER_API_KEY')}",
            "Content-Type": "application/json",
            "HTTP-Referer": os.getenv("SITE_URL", "https://api.useadmesh.com"),
            "X-Title": "AdMesh GEO Analysis"
        }

        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ]
        }

        try:
            response = requests.post("https://openrouter.ai/api/v1/chat/completions",
                                   headers=headers, json=payload)
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except requests.exceptions.RequestException as e:
            logger.error(f"OpenRouter API Error: {e}")
            raise HTTPException(status_code=500, detail="Failed to analyze content with AI")

    async def discover_pages_from_sitemap(self, website: str) -> List[str]:
        """Discover pages from sitemap and return top 10-15 important pages"""
        pages = []

        try:
            # Try common sitemap locations
            sitemap_urls = [
                f"{website}/sitemap.xml",
                f"{website}/sitemap_index.xml",
                f"{website}/robots.txt"
            ]

            async with aiohttp.ClientSession() as session:
                for sitemap_url in sitemap_urls:
                    try:
                        async with session.get(sitemap_url, timeout=10) as response:
                            if response.status == 200:
                                content = await response.text()

                                if sitemap_url.endswith('robots.txt'):
                                    # Extract sitemap URLs from robots.txt
                                    for line in content.split('\n'):
                                        if line.lower().startswith('sitemap:'):
                                            sitemap_url = line.split(':', 1)[1].strip()
                                            pages.extend(await self._parse_sitemap(session, sitemap_url))
                                else:
                                    # Parse XML sitemap
                                    pages.extend(await self._parse_sitemap(session, sitemap_url))

                                if pages:
                                    break
                    except Exception as e:
                        logger.warning(f"Failed to fetch sitemap {sitemap_url}: {e}")
                        continue
        except Exception as e:
            logger.error(f"Error discovering pages from sitemap: {e}")

        # If no sitemap found, use common page patterns
        if not pages:
            common_pages = [
                f"{website}",
                f"{website}/about",
                f"{website}/about-us",
                f"{website}/products",
                f"{website}/services",
                f"{website}/features",
                f"{website}/pricing",
                f"{website}/blog",
                f"{website}/contact",
                f"{website}/faq"
            ]
            pages = common_pages

        # Prioritize important pages and limit to 15
        priority_keywords = ['about', 'product', 'service', 'feature', 'pricing', 'home', 'blog']
        prioritized_pages = []
        other_pages = []

        for page in pages[:30]:  # Limit initial set
            if any(keyword in page.lower() for keyword in priority_keywords):
                prioritized_pages.append(page)
            else:
                other_pages.append(page)

        return (prioritized_pages + other_pages)[:15]

    async def _parse_sitemap(self, session: aiohttp.ClientSession, sitemap_url: str) -> List[str]:
        """Parse XML sitemap and extract URLs"""
        urls = []
        try:
            async with session.get(sitemap_url, timeout=10) as response:
                if response.status == 200:
                    content = await response.text()
                    root = ET.fromstring(content)

                    # Handle different sitemap formats
                    for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                        loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                        if loc_elem is not None:
                            urls.append(loc_elem.text)

                    # Handle sitemap index files
                    for sitemap_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap'):
                        loc_elem = sitemap_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                        if loc_elem is not None:
                            # Recursively parse nested sitemaps
                            nested_urls = await self._parse_sitemap(session, loc_elem.text)
                            urls.extend(nested_urls)
        except Exception as e:
            logger.warning(f"Failed to parse sitemap {sitemap_url}: {e}")

        return urls

    async def analyze_pages_with_llm(self, pages: List[str], brand_name: str) -> List[PageAnalysis]:
        """Analyze multiple pages using LLM for content quality and AI-friendliness"""
        analyzed_pages = []

        async with aiohttp.ClientSession() as session:
            for page_url in pages:
                try:
                    async with session.get(page_url, timeout=15) as response:
                        if response.status == 200:
                            html = await response.text()
                            soup = BeautifulSoup(html, 'html.parser')

                            # Extract content
                            title = soup.find('title')
                            title_text = title.get_text().strip() if title else "No title"

                            # Get main content (remove nav, footer, etc.)
                            for element in soup(['nav', 'footer', 'header', 'script', 'style']):
                                element.decompose()

                            text_content = soup.get_text()
                            # Limit content for LLM analysis
                            content_preview = text_content[:3000] if len(text_content) > 3000 else text_content

                            # Analyze with LLM
                            analysis_prompt = f"""
                            Analyze this webpage content for Generative Engine Optimization (GEO).
                            Brand: {brand_name}
                            Page URL: {page_url}
                            Page Title: {title_text}
                            Content: {content_preview}

                            Evaluate the page on these criteria:
                            1. Value proposition clarity (how clear is what the brand offers?)
                            2. Content structure (headings, lists, FAQ format)
                            3. Brand positioning and mentions
                            4. AI-friendliness (factual claims, evidence, clear statements)

                            Provide a JSON response with:
                            {{
                                "summary": "2-3 sentence summary of the page's GEO strengths and weaknesses",
                                "score": 0-100,
                                "value_proposition_clarity": 0-100,
                                "content_structure": 0-100,
                                "brand_positioning": 0-100,
                                "ai_friendliness": 0-100
                            }}
                            """

                            try:
                                llm_response = self.call_openrouter(analysis_prompt)
                                # Try to parse JSON response
                                analysis_data = json.loads(llm_response)

                                analyzed_pages.append(PageAnalysis(
                                    url=page_url,
                                    title=title_text,
                                    summary=analysis_data.get("summary", "Analysis completed"),
                                    score=analysis_data.get("score", 50)
                                ))
                            except (json.JSONDecodeError, KeyError) as e:
                                logger.warning(f"Failed to parse LLM response for {page_url}: {e}")
                                # Fallback analysis
                                analyzed_pages.append(PageAnalysis(
                                    url=page_url,
                                    title=title_text,
                                    summary="Basic analysis completed - content structure and clarity could be improved",
                                    score=50
                                ))

                except Exception as e:
                    logger.warning(f"Failed to analyze page {page_url}: {e}")
                    continue

        return analyzed_pages

    async def simulate_ai_visibility(self, brand_name: str, industry: str, keywords: List[str]) -> Dict[str, Any]:
        """Simulate AI visibility by generating queries and checking brand mentions"""
        simulated_queries = []
        total_mentions = 0
        positive_mentions = 0

        # Generate industry-specific queries
        query_templates = [
            f"What are the best {industry} companies?",
            f"Top {industry} solutions for small business",
            f"Most reliable {industry} providers",
            f"Leading {industry} platforms comparison",
            f"Best {industry} tools for startups",
            f"Which {industry} service should I choose?",
            f"Recommended {industry} software",
            f"Popular {industry} brands"
        ]

        # Add keyword-based queries
        for keyword in keywords[:5]:  # Use top 5 keywords
            query_templates.extend([
                f"Best {keyword} solutions",
                f"Top {keyword} providers",
                f"{keyword} recommendations"
            ])

        # Simulate AI responses for each query
        for query in query_templates[:10]:  # Limit to 10 queries
            try:
                simulation_prompt = f"""
                Simulate an AI assistant response to this query: "{query}"

                Consider if a brand called "{brand_name}" in the {industry} industry would be mentioned.
                The brand operates in these areas: {', '.join(keywords)}

                Provide a JSON response:
                {{
                    "query": "{query}",
                    "brand_mentioned": true/false,
                    "mention_context": "positive/neutral/negative" (if mentioned),
                    "likelihood_score": 0-100,
                    "reasoning": "brief explanation"
                }}
                """

                llm_response = self.call_openrouter(simulation_prompt)
                simulation_data = json.loads(llm_response)

                simulated_queries.append(simulation_data)

                if simulation_data.get("brand_mentioned", False):
                    total_mentions += 1
                    if simulation_data.get("mention_context") == "positive":
                        positive_mentions += 1

            except Exception as e:
                logger.warning(f"Failed to simulate query '{query}': {e}")
                continue

        # Calculate metrics
        mention_rate = (total_mentions / len(simulated_queries)) * 100 if simulated_queries else 0
        citation_rate = random.uniform(10, 40)  # Simulated for now
        sentiment_score = (positive_mentions / total_mentions * 100) if total_mentions > 0 else 50

        return {
            "promptMentionRate": mention_rate,
            "citationRate": citation_rate,
            "sentimentScore": sentiment_score,
            "totalQueries": len(simulated_queries),
            "totalMentions": total_mentions,
            "positiveMentions": positive_mentions,
            "simulatedQueries": simulated_queries
        }

    async def analyze_website_content(self, website: str) -> Dict[str, Any]:
        """Analyze website content for GEO optimization"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(str(website), timeout=10) as response:
                    if response.status != 200:
                        raise HTTPException(status_code=400, detail="Unable to fetch website content")
                    
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Extract content for analysis
                    text_content = soup.get_text()
                    headings = [h.get_text().strip() for h in soup.find_all(['h1', 'h2', 'h3'])]
                    meta_description = soup.find('meta', attrs={'name': 'description'})
                    meta_desc = meta_description.get('content', '') if meta_description else ''
                    
                    # Analyze structure
                    structure_score = self._analyze_content_structure(soup, text_content)
                    
                    # Analyze factual claims
                    factual_claims_score = self._analyze_factual_claims(text_content)
                    
                    # Analyze AI readability
                    ai_readability_score = self._analyze_ai_readability(text_content, headings)
                    
                    overall_content_score = int((structure_score + factual_claims_score + ai_readability_score) / 3)
                    
                    return {
                        "score": overall_content_score,
                        "structureScore": structure_score,
                        "factualClaimsScore": factual_claims_score,
                        "aiReadabilityScore": ai_readability_score,
                        "headings": headings[:10],  # Top 10 headings
                        "metaDescription": meta_desc,
                        "wordCount": len(text_content.split())
                    }
                    
        except Exception as e:
            logger.error(f"Error analyzing website content: {str(e)}")
            # Return default scores if analysis fails
            return {
                "score": 50,
                "structureScore": 50,
                "factualClaimsScore": 50,
                "aiReadabilityScore": 50,
                "headings": [],
                "metaDescription": "",
                "wordCount": 0
            }
    
    def _analyze_content_structure(self, soup: BeautifulSoup, text: str) -> int:
        """Analyze content structure for AI-friendliness"""
        score = 0
        
        # Check for proper heading hierarchy
        h1_count = len(soup.find_all('h1'))
        h2_count = len(soup.find_all('h2'))
        h3_count = len(soup.find_all('h3'))
        
        if h1_count == 1:  # Exactly one H1
            score += 20
        if h2_count > 0:  # Has H2s
            score += 15
        if h3_count > 0:  # Has H3s
            score += 10
        
        # Check for lists (AI-friendly format)
        ul_count = len(soup.find_all('ul'))
        ol_count = len(soup.find_all('ol'))
        if ul_count + ol_count > 0:
            score += 20
        
        # Check for structured data
        if soup.find('script', type='application/ld+json'):
            score += 15
        
        # Check for clear sections
        if len(soup.find_all(['section', 'article'])) > 0:
            score += 10
        
        # Check for tables (structured data)
        if len(soup.find_all('table')) > 0:
            score += 10
        
        return min(score, 100)
    
    def _analyze_factual_claims(self, text: str) -> int:
        """Analyze presence of factual claims and supporting evidence"""
        score = 0
        
        # Look for numbers and statistics
        number_pattern = r'\b\d+(?:,\d{3})*(?:\.\d+)?(?:%|percent|million|billion|thousand)?\b'
        numbers = re.findall(number_pattern, text.lower())
        if len(numbers) > 5:
            score += 25
        elif len(numbers) > 2:
            score += 15
        
        # Look for authoritative language
        authority_words = ['research', 'study', 'proven', 'certified', 'award', 'recognized', 'verified']
        authority_count = sum(1 for word in authority_words if word in text.lower())
        score += min(authority_count * 5, 25)
        
        # Look for specific claims
        claim_indicators = ['increase', 'improve', 'reduce', 'save', 'faster', 'better', 'more efficient']
        claim_count = sum(1 for indicator in claim_indicators if indicator in text.lower())
        score += min(claim_count * 3, 20)
        
        # Look for evidence indicators
        evidence_words = ['because', 'due to', 'according to', 'based on', 'evidence', 'data shows']
        evidence_count = sum(1 for word in evidence_words if word in text.lower())
        score += min(evidence_count * 5, 30)
        
        return min(score, 100)
    
    def _analyze_ai_readability(self, text: str, headings: List[str]) -> int:
        """Analyze how readable the content is for AI models"""
        score = 0
        
        # Check average sentence length (AI prefers shorter sentences)
        sentences = re.split(r'[.!?]+', text)
        if sentences:
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
            if avg_sentence_length < 20:
                score += 25
            elif avg_sentence_length < 30:
                score += 15
        
        # Check for clear topic sentences
        paragraphs = text.split('\n\n')
        clear_topics = 0
        for para in paragraphs[:10]:  # Check first 10 paragraphs
            if len(para.split()) > 10:  # Substantial paragraph
                first_sentence = para.split('.')[0]
                if any(word in first_sentence.lower() for word in ['this', 'these', 'our', 'we', 'the']):
                    clear_topics += 1
        
        if clear_topics > 5:
            score += 20
        elif clear_topics > 2:
            score += 10
        
        # Check for summary phrases (AI-friendly)
        summary_phrases = ['in summary', 'to summarize', 'in conclusion', 'key benefits', 'main features']
        summary_count = sum(1 for phrase in summary_phrases if phrase in text.lower())
        score += min(summary_count * 10, 25)
        
        # Check heading clarity
        clear_headings = 0
        for heading in headings:
            if len(heading.split()) > 2 and len(heading.split()) < 8:  # Good length
                clear_headings += 1
        
        if clear_headings > 3:
            score += 20
        elif clear_headings > 1:
            score += 10
        
        # Check for FAQ section (very AI-friendly)
        if 'faq' in text.lower() or 'frequently asked' in text.lower():
            score += 10
        
        return min(score, 100)
    
    async def simulate_ai_discoverability(self, company_name: str, industry: str) -> Dict[str, Any]:
        """Simulate AI discoverability analysis"""
        # In a real implementation, this would query actual AI models
        # For now, we'll simulate realistic results
        
        # Generate realistic mention count based on company name length and industry
        base_mentions = random.randint(1, 15)
        if industry and industry.lower() in ['technology', 'software', 'saas']:
            base_mentions += random.randint(0, 10)
        
        # Simulate sentiment analysis
        sentiments = ['positive', 'neutral', 'negative']
        weights = [0.6, 0.3, 0.1]  # Most brands have positive/neutral sentiment
        sentiment = random.choices(sentiments, weights=weights)[0]
        
        # Generate sample queries where the brand might appear
        sample_queries = []
        if industry:
            for query_template in self.ai_queries[:4]:  # Use first 4 templates
                sample_queries.append(query_template.format(industry=industry))
        
        # Calculate discoverability score
        score = min(base_mentions * 6, 100)  # Scale mentions to 0-100
        if sentiment == 'positive':
            score = min(score + 10, 100)
        elif sentiment == 'negative':
            score = max(score - 20, 0)
        
        return {
            "score": score,
            "mentions": base_mentions,
            "sentiment": sentiment,
            "topQueries": sample_queries
        }
    
    def generate_recommendations(self, content_analysis: Dict, ai_discoverability: Dict, brand_name: str, industry: str, analyzed_pages: List[PageAnalysis], overall_score: int) -> List[GEORecommendation]:
        """Generate actionable GEO recommendations using LLM analysis"""
        recommendations = []

        # Generate LLM-powered recommendations based on analysis
        try:
            # Prepare analysis summary for LLM
            pages_summary = []
            for page in analyzed_pages[:5]:  # Top 5 pages
                pages_summary.append({
                    "url": page.url,
                    "title": page.title,
                    "score": page.score,
                    "summary": page.summary
                })

            recommendation_prompt = f"""
            Generate specific, actionable GEO (Generative Engine Optimization) recommendations for {brand_name}, a {industry} company.

            Current Analysis Results:
            - Overall GEO Score: {overall_score}/100
            - Content Structure Score: {content_analysis["structureScore"]}/100
            - Factual Claims Score: {content_analysis["factualClaimsScore"]}/100
            - AI Readability Score: {content_analysis["aiReadabilityScore"]}/100
            - AI Discoverability Score: {ai_discoverability["score"]}/100
            - AI Mentions Found: {ai_discoverability["mentions"]}
            - Sentiment: {ai_discoverability["sentiment"]}

            Top Pages Analyzed:
            {json.dumps(pages_summary, indent=2)}

            Generate 5-6 specific, actionable recommendations based on the proven 5-step AI visibility framework:

            STEP 1 - STRUCTURED AI-FRIENDLY CONTENT:
            - Clear headings and sections (H1, H2, H3 hierarchy)
            - Bullet points and numbered lists for key information
            - Q&A style content and FAQ sections
            - Stand-alone answers that AI can easily extract

            STEP 2 - METADATA AND SCHEMA MARKUP:
            - Optimize page titles, meta descriptions, header tags
            - Implement structured data (schema.org markup)
            - Use FAQPage, HowTo, Product, Organization, Article schemas
            - Add proper alt text and descriptive markup

            STEP 3 - CONSISTENT BRANDING:
            - Standardize brand name, taglines, descriptions across web
            - Link digital presence together (social, profiles, directories)
            - Maintain consistent messaging and entity recognition
            - Update old references and ensure brand consistency

            STEP 4 - THIRD-PARTY MENTIONS AND CITATIONS:
            - Digital PR and media coverage strategies
            - Guest posting and content collaborations
            - Review platform optimization (G2, Capterra, etc.)
            - Industry forum participation and community engagement

            STEP 5 - TOPICAL AUTHORITY BUILDING:
            - Comprehensive coverage of core industry topics
            - Interlinking related content for topic clusters
            - Demonstrate E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness)
            - Keep content current and up-to-date

            For each recommendation, provide:
            - Priority level (high/medium/low)
            - Category (from the 5 steps above)
            - Specific title
            - Detailed description with actionable steps
            - Expected impact on AI visibility

            Return JSON format:
            {{
                "recommendations": [
                    {{
                        "priority": "high",
                        "category": "Structured Content",
                        "title": "Specific actionable title",
                        "description": "Detailed step-by-step implementation guide",
                        "impact": "Specific expected impact on AI visibility and citations"
                    }}
                ]
            }}
            """

            llm_response = self.call_openrouter(recommendation_prompt)
            recommendation_data = json.loads(llm_response)

            # Convert LLM recommendations to GEORecommendation objects
            for rec in recommendation_data.get("recommendations", []):
                recommendations.append(GEORecommendation(
                    priority=rec.get("priority", "medium"),
                    category=rec.get("category", "GEO Strategy"),
                    title=rec.get("title", "Improve GEO performance"),
                    description=rec.get("description", "Focus on creating AI-friendly content"),
                    impact=rec.get("impact", "Better AI visibility")
                ))

        except Exception as e:
            logger.warning(f"Failed to generate LLM recommendations: {e}")
            # Fallback to enhanced rule-based recommendations
            recommendations = self._generate_enhanced_fallback_recommendations(content_analysis, ai_discoverability, brand_name, industry)

        # Ensure we have at least some recommendations
        if not recommendations:
            recommendations = self._generate_enhanced_fallback_recommendations(content_analysis, ai_discoverability, brand_name, industry)

        return recommendations[:6]  # Limit to 6 recommendations

    def _generate_enhanced_fallback_recommendations(self, content_analysis: Dict, ai_discoverability: Dict, brand_name: str, industry: str) -> List[GEORecommendation]:
        """Generate enhanced fallback recommendations based on 5-step AI visibility framework"""
        recommendations = []

        # STEP 1: Structured AI-Friendly Content
        if content_analysis["structureScore"] < 70:
            recommendations.append(GEORecommendation(
                priority="high",
                category="Structured Content",
                title="Implement AI-friendly content structure",
                description="Restructure your content with clear H1-H3 headings, bullet points for key features, numbered lists for processes, and FAQ sections. Create stand-alone answers that AI can easily extract and cite. Focus on making each section self-contained with clear topic statements.",
                impact="Improved AI parsing and 40% higher citation rates in AI responses"
            ))

        # STEP 2: Schema Markup and Metadata
        if content_analysis["aiReadabilityScore"] < 70:
            recommendations.append(GEORecommendation(
                priority="high",
                category="Schema & Metadata",
                title="Implement structured data markup",
                description="Add schema.org markup including Organization, Product, FAQPage, and HowTo schemas. Optimize page titles to include your brand name, write compelling meta descriptions, and ensure all images have descriptive alt text. This helps AI understand your content context.",
                impact="Enhanced AI comprehension and structured data visibility"
            ))

        # STEP 3: Brand Consistency
        recommendations.append(GEORecommendation(
            priority="medium",
            category="Brand Consistency",
            title="Standardize brand presence across the web",
            description=f"Ensure {brand_name} appears consistently across all digital touchpoints. Update your About page, social profiles, and directory listings with identical descriptions. Link all your profiles together and maintain consistent messaging about your {industry} solutions.",
            impact="Stronger entity recognition and brand authority in AI systems"
        ))

        # STEP 4: Third-Party Authority
        if ai_discoverability["score"] < 50:
            recommendations.append(GEORecommendation(
                priority="high",
                category="Third-Party Authority",
                title="Build external citations and mentions",
                description=f"Launch a digital PR campaign targeting {industry} publications. Write guest posts for industry blogs, get listed on review platforms like G2 or Capterra, and participate in industry forums. Each external mention increases your likelihood of AI citation.",
                impact="Higher AI mention rates and improved domain authority"
            ))

        # STEP 5: Topical Authority
        if content_analysis["factualClaimsScore"] < 60:
            recommendations.append(GEORecommendation(
                priority="medium",
                category="Topical Authority",
                title="Establish comprehensive industry expertise",
                description=f"Create in-depth content covering all aspects of {industry}. Develop topic clusters with interlinking articles, include case studies with measurable results, cite industry research, and keep content updated. Demonstrate E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness).",
                impact="Recognition as industry thought leader and preferred AI source"
            ))

        # Always include monitoring recommendation
        recommendations.append(GEORecommendation(
            priority="low",
            category="AI Monitoring",
            title="Monitor AI visibility and adapt strategy",
            description="Regularly test how AI platforms like ChatGPT, Claude, and Perplexity respond to queries in your industry. Track whether your brand gets mentioned and in what context. Use tools to monitor AI visibility trends and adjust your strategy accordingly.",
            impact="Data-driven optimization and continuous improvement in AI rankings"
        ))

        return recommendations

@router.get("/payment-status")
async def get_geo_payment_status(
    user_data=Depends(require_role("brand"))
):
    """
    Check if the user can run a GEO report and get payment status information.
    Returns information about free reports remaining and wallet balance.
    """
    try:
        brand_id = user_data["uid"]

        # Get usage information
        report_count = get_geo_report_count(brand_id)
        remaining_free = get_remaining_free_reports(brand_id)
        payment_required = is_payment_required(brand_id)

        # Get wallet information
        wallet_info = check_wallet_balance(brand_id)

        # Determine if user can run a report
        can_run_report = not payment_required or wallet_info["sufficient"]

        return {
            "can_run_report": can_run_report,
            "payment_required": payment_required,
            "total_reports_generated": report_count,
            "remaining_free_reports": remaining_free,
            "free_reports_limit": FREE_REPORTS_LIMIT,
            "report_cost_dollars": GEO_REPORT_COST_CENTS / 100,
            "wallet_balance_dollars": wallet_info["balance_dollars"],
            "wallet_balance_cents": wallet_info["balance_cents"],
            "sufficient_balance": wallet_info["sufficient"],
            "next_report_cost": {
                "amount_dollars": GEO_REPORT_COST_CENTS / 100 if payment_required else 0,
                "amount_cents": GEO_REPORT_COST_CENTS if payment_required else 0,
                "is_free": not payment_required
            }
        }
    except Exception as e:
        logger.error(f"Error getting GEO payment status for brand {user_data['uid']}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get payment status")

def get_geo_report_count(brand_id: str) -> int:
    """
    Get the total number of GEO reports generated by a brand.
    """
    try:
        db = get_db()
        brand_ref = db.collection("brands").document(brand_id)
        analyses_ref = brand_ref.collection("geo_analyses")

        # Count all analyses for this brand
        analyses = list(analyses_ref.stream())
        return len(analyses)
    except Exception as e:
        logger.error(f"Error counting GEO reports for brand {brand_id}: {str(e)}")
        return 0

def is_payment_required(brand_id: str) -> bool:
    """
    Check if payment is required for the next GEO report.
    Returns True if the brand has used up their free reports.
    """
    report_count = get_geo_report_count(brand_id)
    return report_count >= FREE_REPORTS_LIMIT

def get_remaining_free_reports(brand_id: str) -> int:
    """
    Get the number of remaining free reports for a brand.
    """
    report_count = get_geo_report_count(brand_id)
    remaining = max(0, FREE_REPORTS_LIMIT - report_count)
    return remaining

def check_wallet_balance(brand_id: str) -> Dict[str, Any]:
    """
    Check if the brand has sufficient wallet balance for a GEO report.
    Returns dict with balance info and whether payment is possible.
    """
    try:
        db = get_db()
        wallet_ref = db.collection("wallets").document(brand_id)
        wallet_doc = wallet_ref.get()

        if not wallet_doc.exists:
            return {
                "balance_cents": 0,
                "balance_dollars": 0.0,
                "sufficient": False,
                "required_cents": GEO_REPORT_COST_CENTS,
                "required_dollars": GEO_REPORT_COST_CENTS / 100
            }

        wallet_data = wallet_doc.to_dict()
        balance_cents = wallet_data.get("total_available_balance", 0)
        balance_dollars = balance_cents / 100

        return {
            "balance_cents": balance_cents,
            "balance_dollars": balance_dollars,
            "sufficient": balance_cents >= GEO_REPORT_COST_CENTS,
            "required_cents": GEO_REPORT_COST_CENTS,
            "required_dollars": GEO_REPORT_COST_CENTS / 100
        }
    except Exception as e:
        logger.error(f"Error checking wallet balance for brand {brand_id}: {str(e)}")
        return {
            "balance_cents": 0,
            "balance_dollars": 0.0,
            "sufficient": False,
            "required_cents": GEO_REPORT_COST_CENTS,
            "required_dollars": GEO_REPORT_COST_CENTS / 100
        }

def deduct_geo_report_payment(brand_id: str) -> bool:
    """
    Deduct payment for a GEO report from the brand's wallet.
    Returns True if successful, False otherwise.
    Uses Firestore transaction to ensure ACID properties.
    """
    try:
        db = get_db()

        @firestore.transactional
        def deduct_payment_transaction(transaction):
            # Get wallet document in transaction
            wallet_ref = db.collection("wallets").document(brand_id)
            wallet_doc = wallet_ref.get(transaction=transaction)

            if not wallet_doc.exists:
                raise HTTPException(
                    status_code=400,
                    detail="Wallet not found. Please add funds to your wallet first."
                )

            wallet_data = wallet_doc.to_dict()
            current_balance = wallet_data.get("total_available_balance", 0)

            # Check if sufficient balance
            if current_balance < GEO_REPORT_COST_CENTS:
                raise HTTPException(
                    status_code=400,
                    detail=f"Insufficient wallet balance. Available: ${current_balance/100:.2f}, Required: ${GEO_REPORT_COST_CENTS/100:.2f}"
                )

            # Deduct payment from available balance and add to spent
            new_balance = current_balance - GEO_REPORT_COST_CENTS
            transaction.update(wallet_ref, {
                "total_available_balance": new_balance,
                "total_balance_spent": firestore.Increment(GEO_REPORT_COST_CENTS),
                "updated_at": firestore.SERVER_TIMESTAMP
            })

            # Create transaction record
            tx_ref = wallet_ref.collection("transactions").document()
            tx_data = {
                "type": "geo_report_payment",
                "amount": -GEO_REPORT_COST_CENTS,  # Negative for debit
                "currency": "USD",
                "description": f"GEO Report Analysis - ${GEO_REPORT_COST_CENTS/100:.2f}",
                "timestamp": firestore.SERVER_TIMESTAMP,
                "status": "completed",
                "category": "geo_report",
                "reference_type": "geo_analysis"
            }
            transaction.set(tx_ref, tx_data)

            return new_balance

        # Execute transaction
        transaction = db.transaction()
        new_balance = deduct_payment_transaction(transaction)

        logger.info(f"Successfully deducted ${GEO_REPORT_COST_CENTS/100:.2f} from brand {brand_id} wallet. New balance: ${new_balance/100:.2f}")
        return True

    except HTTPException:
        # Re-raise HTTP exceptions (insufficient balance, etc.)
        raise
    except Exception as e:
        logger.error(f"Error deducting GEO report payment for brand {brand_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process payment. Please try again."
        )

@router.post("/check")
async def run_geo_check(
    request: GEOCheckRequest,
    user_data=Depends(require_role("brand"))
):
    """
    Run a comprehensive GEO (Generative Engine Optimization) analysis
    for a brand's website and online presence.

    First 2 reports are free, subsequent reports cost $9.00 each.
    """
    try:
        db = get_db()
        brand_id = request.brand_id or user_data["uid"]

        # Check if payment is required and validate wallet balance
        payment_required = is_payment_required(brand_id)

        if payment_required:
            # Check wallet balance before proceeding
            wallet_info = check_wallet_balance(brand_id)
            if not wallet_info["sufficient"]:
                remaining_free = get_remaining_free_reports(brand_id)
                raise HTTPException(
                    status_code=402,  # Payment Required
                    detail={
                        "error": "Insufficient wallet balance for GEO report",
                        "message": f"GEO reports cost ${GEO_REPORT_COST_CENTS/100:.2f} each after {FREE_REPORTS_LIMIT} free reports.",
                        "remaining_free_reports": remaining_free,
                        "wallet_balance": wallet_info["balance_dollars"],
                        "required_amount": wallet_info["required_dollars"],
                        "payment_required": True
                    }
                )

            # Deduct payment before running analysis
            logger.info(f"Deducting ${GEO_REPORT_COST_CENTS/100:.2f} from brand {brand_id} wallet for GEO report")
            deduct_geo_report_payment(brand_id)
        else:
            remaining_free = get_remaining_free_reports(brand_id)
            logger.info(f"Running free GEO report for brand {brand_id}. {remaining_free - 1} free reports remaining after this one.")

        # Fetch brand data from Firestore
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        brand_data = brand_doc.to_dict()
        website = brand_data.get("website")
        brand_name = brand_data.get("company_name") or brand_data.get("brand_name")

        if not website:
            raise HTTPException(status_code=400, detail="Brand website not found. Please update your brand profile.")

        if not brand_name:
            raise HTTPException(status_code=400, detail="Brand name not found. Please update your brand profile.")

        # Get keywords from products
        products_query = db.collection("products").where("brand_id", "==", brand_id)
        products = products_query.stream()

        keywords = []
        categories = []
        for product in products:
            product_data = product.to_dict()
            if "keywords" in product_data:
                keywords.extend(product_data["keywords"])
            if "categories" in product_data:
                categories.extend(product_data["categories"])

        # Remove duplicates and get unique keywords/categories
        keywords = list(set(keywords))[:10]  # Limit to top 10
        categories = list(set(categories))[:5]  # Limit to top 5
        industry = categories[0] if categories else "technology"

        analyzer = GEOAnalyzer()

        # Discover and analyze pages
        logger.info(f"Starting GEO analysis for {brand_name} - {website}")
        pages = await analyzer.discover_pages_from_sitemap(website)
        analyzed_pages = await analyzer.analyze_pages_with_llm(pages, brand_name)

        # Run AI visibility simulation
        ai_visibility = await analyzer.simulate_ai_visibility(brand_name, industry, keywords)

        # Calculate website optimization score from analyzed pages
        if analyzed_pages:
            website_optimization_score = sum(page.score for page in analyzed_pages) // len(analyzed_pages)
        else:
            website_optimization_score = 50

        # Calculate weighted overall score
        prompt_mention_rate = ai_visibility["promptMentionRate"]
        citation_rate = ai_visibility["citationRate"]
        sentiment_tone = ai_visibility["sentimentScore"]

        overall_score = int(
            prompt_mention_rate * 0.4 +
            citation_rate * 0.2 +
            website_optimization_score * 0.3 +
            sentiment_tone * 0.1
        )

        # Legacy format for compatibility
        content_analysis = {
            "score": website_optimization_score,
            "structureScore": website_optimization_score,
            "factualClaimsScore": website_optimization_score,
            "aiReadabilityScore": website_optimization_score
        }

        ai_discoverability = {
            "score": int(prompt_mention_rate),
            "mentions": ai_visibility["totalMentions"],
            "sentiment": "positive" if sentiment_tone > 70 else "neutral" if sentiment_tone > 40 else "negative",
            "topQueries": [q.get("query", "") for q in ai_visibility["simulatedQueries"][:5]]
        }

        competitive_analysis = {
            "shareOfVoice": random.randint(5, 35),  # Simulated for now
            "competitorMentions": [
                {"name": "Competitor A", "mentions": random.randint(10, 50)},
                {"name": "Competitor B", "mentions": random.randint(5, 30)},
                {"name": "Competitor C", "mentions": random.randint(3, 25)}
            ]
        }

        # Generate recommendations
        recommendations = analyzer.generate_recommendations(content_analysis, ai_discoverability, brand_name, industry, analyzed_pages, overall_score)

        # Store complete results in Firestore for history
        analysis_result = {
            "brand_id": brand_id,
            "brand_name": brand_name,
            "website_analyzed": website,
            "industry": industry,
            "keywords": keywords,

            # Core metrics
            "overall_score": overall_score,
            "prompt_mention_rate": prompt_mention_rate,
            "citation_rate": citation_rate,
            "website_optimization": website_optimization_score,
            "sentiment_tone": sentiment_tone,

            # Detailed analysis data
            "ai_discoverability": ai_discoverability,
            "content_optimization": content_analysis,
            "competitive_analysis": competitive_analysis,

            # Analyzed pages (convert to dict for Firestore)
            "analyzed_pages": [
                {
                    "url": page.url,
                    "title": page.title,
                    "summary": page.summary,
                    "score": page.score
                } for page in analyzed_pages
            ],

            # Simulated queries
            "simulated_queries": ai_visibility["simulatedQueries"],

            # Recommendations (convert to dict for Firestore)
            "recommendations": [
                {
                    "priority": rec.priority,
                    "category": rec.category,
                    "title": rec.title,
                    "description": rec.description,
                    "impact": rec.impact
                } for rec in recommendations
            ],

            # Metadata
            "analyzed_pages_count": len(analyzed_pages),
            "total_queries_simulated": len(ai_visibility["simulatedQueries"]),
            "total_mentions": ai_visibility["totalMentions"],
            "created_at": firestore.SERVER_TIMESTAMP,
            "analysis_version": "1.5"  # For future compatibility
        }

        # Store in brands/{brandId}/geo_analyses subcollection with auto-generated ID
        brand_ref = db.collection("brands").document(brand_id)
        analysis_doc_ref = brand_ref.collection("geo_analyses").add(analysis_result)
        analysis_id = analysis_doc_ref[1].id  # Get the document ID

        logger.info(f"Stored GEO analysis with ID: {analysis_id} for brand: {brand_id}")

        # Return the analysis response with the stored analysis ID
        response = GEOAnalysisResponse(
            overallScore=overall_score,
            promptMentionRate=prompt_mention_rate,
            citationRate=citation_rate,
            websiteOptimization=website_optimization_score,
            sentimentTone=int(sentiment_tone),
            aiDiscoverability=ai_discoverability,
            contentOptimization=content_analysis,
            competitiveAnalysis=competitive_analysis,
            analyzedPages=analyzed_pages,
            simulatedQueries=ai_visibility["simulatedQueries"],
            recommendations=recommendations,
            analyzedAt=datetime.now(),
            brandId=brand_id
        )

        # Add the analysis ID to the response for reference
        response_dict = response.dict()
        response_dict["analysisId"] = analysis_id

        return response_dict

    except Exception as e:
        logger.error(f"GEO analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"GEO analysis failed: {str(e)}")

@router.get("/history")
async def get_geo_analysis_history(
    limit: int = 10,
    offset: int = 0,
    user_data=Depends(require_role("brand"))
):
    """
    Get historical GEO analysis reports for the authenticated brand.
    Returns a paginated list of past analyses with summary information.
    """
    try:
        db = get_db()
        brand_id = user_data["uid"]

        # Query historical analyses for this brand from subcollection
        brand_ref = db.collection("brands").document(brand_id)
        analyses_ref = brand_ref.collection("geo_analyses")
        query = analyses_ref.order_by("created_at", direction=firestore.Query.DESCENDING)

        # Apply pagination
        if offset > 0:
            # Get the document to start after for pagination
            all_docs = list(query.limit(offset).stream())
            if all_docs:
                query = query.start_after(all_docs[-1])

        # Get the requested page of results
        docs = list(query.limit(limit).stream())

        # Convert to response format
        history = []
        for doc in docs:
            data = doc.to_dict()

            # Convert Firestore timestamp to datetime
            created_at = data.get("created_at")
            if hasattr(created_at, 'timestamp'):
                created_at = datetime.fromtimestamp(created_at.timestamp())
            elif isinstance(created_at, datetime):
                created_at = created_at
            else:
                created_at = datetime.now()

            history.append({
                "id": doc.id,
                "created_at": created_at.isoformat(),
                "overall_score": data.get("overall_score", 0),
                "website_analyzed": data.get("website_analyzed", ""),
                "brand_name": data.get("brand_name", ""),
                "analyzed_pages_count": data.get("analyzed_pages_count", 0),
                "total_queries_simulated": data.get("total_queries_simulated", 0),
                "prompt_mention_rate": data.get("prompt_mention_rate", 0),
                "citation_rate": data.get("citation_rate", 0),
                "website_optimization": data.get("website_optimization", 0),
                "sentiment_tone": data.get("sentiment_tone", 0),
                "analysis_version": data.get("analysis_version", "1.0")
            })

        # Get total count for pagination info
        total_count = len(list(analyses_ref.stream()))

        return {
            "history": history,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + len(history) < total_count
            }
        }

    except Exception as e:
        logger.error(f"Failed to fetch GEO analysis history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch analysis history: {str(e)}")


@router.get("/history/{analysis_id}")
async def get_geo_analysis_by_id(
    analysis_id: str,
    user_data=Depends(require_role("brand"))
):
    """
    Get a specific historical GEO analysis by ID.
    Returns the complete analysis data in the same format as a fresh analysis.
    """
    try:
        db = get_db()
        brand_id = user_data["uid"]

        # Get the specific analysis document from brand's subcollection
        brand_ref = db.collection("brands").document(brand_id)
        doc_ref = brand_ref.collection("geo_analyses").document(analysis_id)
        doc = doc_ref.get()

        if not doc.exists:
            raise HTTPException(status_code=404, detail="Analysis not found")

        data = doc.to_dict()

        # No need to verify brand ownership since we're already in the brand's subcollection

        # Convert Firestore timestamp to datetime
        created_at = data.get("created_at")
        if hasattr(created_at, 'timestamp'):
            created_at = datetime.fromtimestamp(created_at.timestamp())
        elif isinstance(created_at, datetime):
            created_at = created_at
        else:
            created_at = datetime.now()

        # Convert stored data back to the expected response format
        analyzed_pages = [
            PageAnalysis(
                url=page.get("url", ""),
                title=page.get("title", ""),
                summary=page.get("summary", ""),
                score=page.get("score", 0)
            ) for page in data.get("analyzed_pages", [])
        ]

        recommendations = [
            GEORecommendation(
                priority=rec.get("priority", "medium"),
                category=rec.get("category", ""),
                title=rec.get("title", ""),
                description=rec.get("description", ""),
                impact=rec.get("impact", "")
            ) for rec in data.get("recommendations", [])
        ]

        # Return in the same format as a fresh analysis
        return {
            "analysisId": analysis_id,
            "overallScore": data.get("overall_score", 0),
            "promptMentionRate": data.get("prompt_mention_rate", 0),
            "citationRate": data.get("citation_rate", 0),
            "websiteOptimization": data.get("website_optimization", 0),
            "sentimentTone": data.get("sentiment_tone", 0),
            "aiDiscoverability": data.get("ai_discoverability", {}),
            "contentOptimization": data.get("content_optimization", {}),
            "competitiveAnalysis": data.get("competitive_analysis", {}),
            "analyzedPages": [page.dict() for page in analyzed_pages],
            "simulatedQueries": data.get("simulated_queries", []),
            "recommendations": [rec.dict() for rec in recommendations],
            "analyzedAt": created_at.isoformat(),
            "brandId": brand_id,
            "websiteAnalyzed": data.get("website_analyzed", ""),
            "brandName": data.get("brand_name", ""),
            "isHistorical": True,
            "analysisVersion": data.get("analysis_version", "1.0")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to fetch GEO analysis {analysis_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch analysis: {str(e)}")


@router.get("/reports")
async def get_geo_reports(
    page: int = 1,
    limit: int = 5,
    user_data=Depends(require_role("brand"))
):
    """
    Get paginated GEO analysis reports for the authenticated brand.
    Returns reports with pagination metadata compatible with the dashboard frontend.
    """
    try:
        db = get_db()
        brand_id = user_data["uid"]

        # Calculate offset from page number
        offset = (page - 1) * limit

        # Query historical analyses for this brand from subcollection
        brand_ref = db.collection("brands").document(brand_id)
        analyses_ref = brand_ref.collection("geo_analyses")
        query = analyses_ref.order_by("created_at", direction=firestore.Query.DESCENDING)

        # Get total count for pagination info
        total_count = len(list(analyses_ref.stream()))

        # Apply pagination
        if offset > 0:
            # Get the document to start after for pagination
            all_docs = list(query.limit(offset).stream())
            if all_docs:
                query = query.start_after(all_docs[-1])

        # Get the requested page of results
        docs = list(query.limit(limit).stream())

        # Convert to response format
        reports = []
        for doc in docs:
            data = doc.to_dict()

            # Convert Firestore timestamp to datetime
            created_at = data.get("created_at")
            if hasattr(created_at, 'timestamp'):
                created_at = datetime.fromtimestamp(created_at.timestamp())
            elif isinstance(created_at, datetime):
                created_at = created_at
            else:
                created_at = datetime.now()

            reports.append({
                "id": doc.id,
                "created_at": created_at.isoformat(),
                "overall_score": data.get("overall_score", 0),
                "website_analyzed": data.get("website_analyzed", ""),
                "brand_name": data.get("brand_name", ""),
                "analyzed_pages_count": data.get("analyzed_pages_count", 0),
                "total_queries_simulated": data.get("total_queries_simulated", 0),
                "prompt_mention_rate": data.get("prompt_mention_rate", 0),
                "citation_rate": data.get("citation_rate", 0),
                "website_optimization": data.get("website_optimization", 0),
                "sentiment_tone": data.get("sentiment_tone", 0),
                "analysis_version": data.get("analysis_version", "1.0")
            })

        # Calculate pagination metadata
        has_more = offset + len(reports) < total_count
        total_pages = (total_count + limit - 1) // limit  # Ceiling division

        return {
            "reports": reports,
            "hasMore": has_more,
            "totalCount": total_count,
            "currentPage": page,
            "totalPages": total_pages
        }

    except Exception as e:
        logger.error(f"Failed to fetch GEO reports: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch reports: {str(e)}")


@router.get("/geo-check/info")
async def get_geo_info():
    """
    Get information about GEO (Generative Engine Optimization) and its importance.
    """
    return {
        "title": "Generative Engine Optimization (GEO)",
        "description": "GEO is the practice of optimizing content for AI-powered search engines and language models like ChatGPT, Claude, and Perplexity.",
        "keyDifferences": {
            "seo": {
                "focus": "Ranking high in search results pages",
                "methods": ["Keywords", "Backlinks", "Page optimization"],
                "goal": "Click-through rates"
            },
            "geo": {
                "focus": "Being cited in AI-generated responses",
                "methods": ["Factual claims", "Structured content", "Authority building"],
                "goal": "Reference rates in AI outputs"
            }
        },
        "benefits": [
            "Early adopter advantage in AI-driven search",
            "Higher trust from AI-mediated recommendations",
            "Future-proof marketing strategy",
            "Better content quality and authority"
        ],
        "gettingStarted": [
            "Structure content with clear headings and bullet points",
            "Include factual claims with supporting evidence",
            "Create FAQ sections and summary content",
            "Build authority through reputable publications",
            "Monitor AI mentions and sentiment"
        ]
    }
