from fastapi import APIRouter, Request, Depends, HTTPException
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import stripe
import os
from firebase_admin import firestore
from firebase.config import get_db
from auth.deps import require_role
import logging
from datetime import datetime

router = APIRouter()
db = get_db()

stripe.api_key = os.environ.get("STRIPE_SECRET_KEY")
DOMAIN = os.environ.get("STRIPE_DOMAIN")  # e.g. https://admesh.com

logger = logging.getLogger("stripe_webhook")
logging.basicConfig(level=logging.INFO)

class CheckoutRequest(BaseModel):
    # All monetary values must be integers in cents
    amount: int  # Total amount in cents (including fees)
    credit_amount: int = None  # Actual credit amount in cents (without fees)

@router.post("/create-checkout-session")
async def create_checkout_session(
    data: CheckoutRequest,
    user=Depends(require_role("brand"))
):
    # Validate the amount (in cents)
    if data.amount < 100:  # 100 cents = $1.00
        raise HTTPException(status_code=400, detail="Minimum amount is $1.00 (100 cents)")

    try:
        session = stripe.checkout.Session.create(
            payment_method_types=["card"],
            line_items=[{
                "price_data": {
                    "currency": "usd",
                    "unit_amount": data.amount,  # Already in cents
                    "product_data": {
                        "name": "AdMesh Wallet Top-up",
                    },
                },
                "quantity": 1,
            }],
            mode="payment",
            success_url=f"{DOMAIN}/dashboard/brand/billing?success=true",
            cancel_url=f"{DOMAIN}/dashboard/brand/billing?canceled=true",
            metadata={
                "brand_id": user["uid"],
                "amount": str(data.credit_amount or data.amount)
            }
        )
        return { "session_url": session.url }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/webhook")
async def stripe_webhook(request: Request):
    payload = await request.body()
    sig_header = request.headers.get("stripe-signature")
    endpoint_secret = os.environ.get("STRIPE_WEBHOOK_SECRET")

    logger.info("Received webhook request")

    try:
        event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        logger.info(f"Webhook event type: {event['type']}")
    except stripe.error.SignatureVerificationError:
        logger.error("Invalid signature for webhook")
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Handle one-time payment checkout completion
    if event["type"] == "checkout.session.completed":
        session = event["data"]["object"]
        metadata = session.get("metadata", {})
        logger.info(f"Session metadata: {metadata}")
        
        # Handle regular one-time payment
        await _handle_payment_checkout(session)

    return {"status": "success"}

# Helper function to handle one-time payment checkout
async def _handle_payment_checkout(session):
    """Handle successful one-time payment checkout"""
    metadata = session.get("metadata", {})
    
    try:
        brand_id = metadata["brand_id"]
        amount = int(metadata["amount"])  # Amount in cents
    except (KeyError, ValueError, TypeError) as e:
        logger.error(f"Invalid metadata: {e}")
        raise HTTPException(status_code=400, detail="Invalid metadata in webhook")

    # Get the brand document
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = await brand_ref.get()

    if not brand_doc.exists:
        logger.error(f"Brand not found: {brand_id}")
        return

    # Update the brand's wallet balance
    try:
        # Start a transaction to ensure atomic update
        @firestore.transactional
        async def update_balance(transaction, brand_ref, amount):
            brand_doc = await brand_ref.get(transaction=transaction)
            if not brand_doc.exists:
                return None
                
            brand_data = brand_doc.to_dict()
            current_balance = brand_data.get("wallet_balance_cents", 0)
            new_balance = current_balance + amount
            
            transaction.update(brand_ref, {
                "wallet_balance_cents": new_balance,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Add transaction record
            tx_data = {
                "type": "deposit",
                "amount": amount,
                "timestamp": firestore.SERVER_TIMESTAMP,
                "description": f"Wallet top-up via Stripe (${amount/100:.2f})",
                "status": "completed"
            }
            
            tx_ref = db.collection("payments").document(brand_id).collection("transactions").document()
            transaction.set(tx_ref, tx_data)
            
            return new_balance

        # Run the transaction
        transaction = db.transaction()
        new_balance = await update_balance(transaction, brand_ref, amount)
        
        if new_balance is None:
            logger.error(f"Failed to update balance for brand {brand_id}")
            return
            
        logger.info(f"Updated wallet balance for brand {brand_id}. New balance: {new_balance} cents")
        
    except Exception as e:
        logger.exception(f"Error updating wallet balance: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update wallet balance")

    logger.info(f"Transaction completed for brand_id {brand_id}, amount: {amount}")


class Transaction(BaseModel):
    id: str
    type: str
    amount: int
    timestamp: Dict[str, int]
    description: str
    status: str

@router.get("/wallet")
def get_wallet(user=Depends(require_role("brand"))) -> Dict[str, Any]:
    """Get wallet information including balance and transaction history."""
    brand_id = user["uid"]

    # Get wallet data from wallets collection
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        # If no wallet document exists yet, create a new one
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        # Create the wallet document with default values
        wallet_data = {
            "brand_id": brand_id,
            "total_available_balance": 0.0,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        wallet_ref.set(wallet_data)
        wallet_data = wallet_data  # Use the data we just created
    else:
        wallet_data = wallet_doc.to_dict()

    return {
        "wallet_balance": wallet_data.get("total_available_balance", 0),  # For backward compatibility
        "total_spent": wallet_data.get("total_balance_spent", 0),  # For backward compatibility
        "current_total_budget": wallet_data.get("total_budget_allocated", 0),  # For backward compatibility
        "total_available_balance": wallet_data.get("total_available_balance", 0),
        "total_promo_available_balance": wallet_data.get("total_promo_available_balance", 0),
        "total_promo_balance_spent": wallet_data.get("total_promo_balance_spent", 0),
        "total_balance_spent": wallet_data.get("total_balance_spent", 0),
        "total_budget_allocated": wallet_data.get("total_budget_allocated", 0)
    }
    
    # Get data from wallet document
    wallet_data = wallet_doc.to_dict()

    # Return both legacy fields and new wallet fields
    return {
        # Legacy fields for backward compatibility
        "wallet_balance": wallet_data.get("total_available_balance", 0),
        "total_spent": wallet_data.get("total_balance_spent", 0),
        "current_total_budget": wallet_data.get("total_budget_allocated", 0),
        # New wallet fields
        "total_available_balance": wallet_data.get("total_available_balance", 0),
        "total_promo_available_balance": wallet_data.get("total_promo_available_balance", 0),
        "total_promo_balance_spent": wallet_data.get("total_promo_balance_spent", 0),
        "total_balance_spent": wallet_data.get("total_balance_spent", 0),
        "total_budget_allocated": wallet_data.get("total_budget_allocated", 0)
    }


@router.get("/transactions")
def get_transactions(user=Depends(require_role("brand"))) -> Dict[str, List[Dict[str, Any]]]:
    """Get transaction history for the authenticated brand."""
    brand_id = user["uid"]
    transactions: List[Dict[str, Any]] = []

    try:
        # Get transactions from the wallet transactions collection
        wallet_tx_ref = db.collection("wallets").document(brand_id).collection("transactions")
        wallet_tx_query = wallet_tx_ref.order_by("timestamp", direction=firestore.Query.DESCENDING)
        wallet_tx_docs = wallet_tx_query.stream()

        for doc in wallet_tx_docs:
            tx = doc.to_dict()
            tx["id"] = doc.id
            tx["source"] = "wallet"  # Add source to distinguish from payment transactions

            # Convert timestamp to a serializable format
            if "timestamp" in tx and tx["timestamp"] is not None:
                if hasattr(tx["timestamp"], "seconds"):
                    tx["timestamp"] = {
                        "seconds": tx["timestamp"].seconds,
                        "nanoseconds": tx["timestamp"].nanoseconds
                    }

            transactions.append(tx)

        # Get transactions from the payments collection
        payment_tx_ref = db.collection("payments").document(brand_id).collection("transactions")
        payment_tx_query = payment_tx_ref.order_by("timestamp", direction=firestore.Query.DESCENDING)
        payment_tx_docs = payment_tx_query.stream()

        for doc in payment_tx_docs:
            tx = doc.to_dict()
            tx["id"] = f"payment_{doc.id}"  # Add prefix to avoid ID conflicts
            tx["source"] = "payments"  # Add source to distinguish from wallet transactions

            # Convert timestamp to a serializable format
            if "timestamp" in tx and tx["timestamp"] is not None:
                if hasattr(tx["timestamp"], "seconds"):
                    tx["timestamp"] = {
                        "seconds": tx["timestamp"].seconds,
                        "nanoseconds": tx["timestamp"].nanoseconds
                    }

            transactions.append(tx)

        # Sort all transactions by timestamp (newest first)
        transactions.sort(key=lambda x: (
            x.get("timestamp", {}).get("seconds", 0) if isinstance(x.get("timestamp"), dict) else 0
        ), reverse=True)

        return {"transactions": transactions}

    except Exception as e:
        logger.error(f"Error fetching transactions: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))