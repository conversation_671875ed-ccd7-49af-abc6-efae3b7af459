from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel
from typing import Optional, List
from firebase.config import get_db
from google.cloud import firestore
from auth.deps import verify_firebase_token, require_role
import logging

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

class UpdateAgentOnboardingPayload(BaseModel):
    name: Optional[str] = None
    agent_type: Optional[str] = None
    agent_url: Optional[str] = None
    user_count: Optional[str] = None
    agent_id: Optional[str] = None
    onboarding_status: Optional[str] = None

@router.patch("/agent/update-onboarding")
async def update_agent_onboarding(
    payload: UpdateAgentOnboardingPayload,
    decoded_token = Depends(require_role("agent"))
):
    """Update agent onboarding status and profile information"""
    uid = decoded_token["uid"]

    # Filter out None values
    update_data = {k: v for k, v in payload.model_dump().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    try:
        # Update agent document
        agent_ref = db.collection("agents").document(uid)
        agent_ref.update(update_data)

        # If onboarding_status is being updated to "integration" or "completed"
        if payload.onboarding_status in ["integration", "completed"]:
            from firebase_admin import auth as firebase_auth
            import secrets
            import string
            import time

            # Get current custom claims
            user = firebase_auth.get_user(uid)
            current_claims = user.custom_claims or {}

            # Update onboarding_status in custom claims
            current_claims["onboarding_status"] = payload.onboarding_status

            # Set updated custom claims
            firebase_auth.set_custom_user_claims(uid, current_claims)

            # Only generate API keys when status is "completed"
            if payload.onboarding_status == "completed":
                # Generate test and production API keys
                agent_id = payload.agent_id
                now = int(time.time())

                # Helper function to generate API key
                def generate_api_key(key_type):
                    prefix = "sk_test_" if key_type == "test" else "sk_live_"
                    alphabet = string.ascii_letters + string.digits
                    random_part = ''.join(secrets.choice(alphabet) for _ in range(32))
                    return f"{prefix}{random_part}"

                # Create test API key
                test_key = generate_api_key("test")
                test_key_data = {
                    "agent_id": agent_id,
                    "key": test_key,
                    "type": "test",
                    "created_at": now,
                    "last_used": None,
                    "is_active": True,
                    "name": "Test API Key"
                }
                db.collection("api_keys").document().set(test_key_data)

                # Create production API key
                prod_key = generate_api_key("production")
                prod_key_data = {
                    "agent_id": agent_id,
                    "key": prod_key,
                    "type": "production",
                    "created_at": now,
                    "last_used": None,
                    "is_active": True,
                    "name": "Production API Key"
                }
                db.collection("api_keys").document().set(prod_key_data)

                # Return both keys along with the update status
                return {
                    "status": "success",
                    "updated": update_data,
                    "api_keys": {
                        "test": test_key,
                        "production": prod_key
                    }
                }

            # For integration status, just return success
            return {
                "status": "success",
                "updated": update_data
            }

        return {"status": "success", "updated": update_data}

    except Exception as e:
        logger.exception(f"Failed to update agent onboarding: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update agent onboarding: {str(e)}")

@router.get("/agent/onboarding/status")
async def get_agent_onboarding_status(decoded_token = Depends(require_role("agent"))):
    """Get agent onboarding status"""
    uid = decoded_token["uid"]

    try:
        # Get agent document
        agent_ref = db.collection("agents").document(uid)
        agent_doc = agent_ref.get()

        if not agent_doc.exists:
            raise HTTPException(status_code=404, detail="Agent not found")

        agent_data = agent_doc.to_dict()

        # Return onboarding status and data
        return {
            "onboarding_status": agent_data.get("onboarding_status", "pending"),
            "agent_id": agent_data.get("agent_id", None),
            "name": agent_data.get("name", None),
            "agent_type": agent_data.get("agent_type", None),
            "agent_url": agent_data.get("agent_url", None),
            "user_count": agent_data.get("user_count", None)
        }

    except Exception as e:
        logger.exception(f"Failed to get agent onboarding status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent onboarding status: {str(e)}")

@router.get("/agent/onboarding/data")
async def get_agent_onboarding_data(decoded_token = Depends(require_role("agent"))):
    """Get agent onboarding data including API keys if available"""
    uid = decoded_token["uid"]

    try:
        # Get agent document
        agent_ref = db.collection("agents").document(uid)
        agent_doc = agent_ref.get()

        if not agent_doc.exists:
            raise HTTPException(status_code=404, detail="Agent not found")

        agent_data = agent_doc.to_dict()

        # Get API keys if they exist
        api_keys = {}
        if agent_data.get("onboarding_status") == "completed":
            agent_id = agent_data.get("agent_id")
            if agent_id:
                # Query for active API keys
                keys_query = db.collection("api_keys").where("agent_id", "==", agent_id).where("is_active", "==", True).stream()

                for key_doc in keys_query:
                    key_data = key_doc.to_dict()
                    key_type = key_data.get("type")
                    if key_type in ["test", "production"]:
                        api_keys[key_type] = key_data.get("key")

        # Return full onboarding data
        return {
            "onboarding_status": agent_data.get("onboarding_status", "pending"),
            "agent_id": agent_data.get("agent_id", None),
            "name": agent_data.get("name", None),
            "agent_type": agent_data.get("agent_type", None),
            "agent_url": agent_data.get("agent_url", None),
            "api_keys": api_keys
        }

    except Exception as e:
        logger.exception(f"Failed to get agent onboarding data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent onboarding data: {str(e)}")
