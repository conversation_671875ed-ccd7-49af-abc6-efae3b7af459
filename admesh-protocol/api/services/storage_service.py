"""
Firebase Storage Service for AdMesh
Handles saving and retrieving generated ads from Firebase Storage
"""

import os
import json
import logging
import uuid
import aiohttp
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from firebase_admin import storage
from firebase.config import get_db
from config.config_manager import get_firebase_config
import tempfile
from urllib.parse import urlparse
import mimetypes

logger = logging.getLogger(__name__)

class StorageService:
    """Service for handling Firebase Storage operations for generated ads"""

    def __init__(self):
        # Get the storage bucket name from configuration
        firebase_config = get_firebase_config()
        bucket_name = firebase_config.get("storage_bucket")

        if not bucket_name:
            raise ValueError("Storage bucket name not found in Firebase configuration")

        self.bucket = storage.bucket(bucket_name)
        logger.info(f"✅ Initialized Firebase Storage with bucket: {bucket_name}")

    async def _download_and_store_media(
        self,
        media_url: str,
        storage_path: str,
        media_type: str = "image"
    ) -> Dict[str, Any]:
        """
        Download media from external URL and store in Firebase Storage

        Args:
            media_url: External URL of the media file
            storage_path: Path where to store the file in Firebase Storage
            media_type: Type of media (image, video)

        Returns:
            Dict containing storage information and metadata
        """
        try:
            # Parse URL to get file extension
            parsed_url = urlparse(media_url)

            # Determine file extension and content type
            if media_type == "image":
                # Default to .png for images if no extension found
                file_extension = ".png"
                content_type = "image/png"

                # Try to detect from URL
                if parsed_url.path:
                    detected_ext = os.path.splitext(parsed_url.path)[1].lower()
                    if detected_ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                        file_extension = detected_ext
                        content_type = f"image/{detected_ext[1:]}"
                        if detected_ext in ['.jpg', '.jpeg']:
                            content_type = "image/jpeg"
            else:
                # Default to .mp4 for videos
                file_extension = ".mp4"
                content_type = "video/mp4"

                if parsed_url.path:
                    detected_ext = os.path.splitext(parsed_url.path)[1].lower()
                    if detected_ext in ['.mp4', '.mov', '.avi', '.webm']:
                        file_extension = detected_ext
                        content_type = f"video/{detected_ext[1:]}"

            # Add file extension to storage path
            full_storage_path = f"{storage_path}{file_extension}"

            # Download the file
            async with aiohttp.ClientSession() as session:
                async with session.get(media_url) as response:
                    if response.status == 200:
                        file_content = await response.read()
                        file_size = len(file_content)

                        # Create temporary file
                        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                            temp_file.write(file_content)
                            temp_file_path = temp_file.name

                        try:
                            # Upload to Firebase Storage
                            blob = self.bucket.blob(full_storage_path)
                            blob.upload_from_filename(temp_file_path, content_type=content_type)

                            # Get the public URL
                            blob.make_public()
                            public_url = blob.public_url

                            logger.info(f"Successfully stored media file: {full_storage_path}")

                            return {
                                "success": True,
                                "storage_path": full_storage_path,
                                "public_url": public_url,
                                "file_size": file_size,
                                "content_type": content_type,
                                "original_url": media_url
                            }

                        finally:
                            # Clean up temporary file
                            if os.path.exists(temp_file_path):
                                os.unlink(temp_file_path)
                    else:
                        raise Exception(f"Failed to download media: HTTP {response.status}")

        except Exception as e:
            logger.error(f"Error downloading and storing media from {media_url}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "original_url": media_url
            }
    
    async def save_ads_to_storage(
        self, 
        brand_id: str, 
        session_id: str,
        ads: List[Dict[str, Any]], 
        metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Save generated ads to Firebase Storage
        
        Args:
            brand_id: The brand's UID
            session_id: The ad generation session ID
            ads: List of generated ads
            metadata: Session metadata (prompt, platforms, etc.)
            
        Returns:
            Dict containing storage information and saved ad IDs
        """
        try:
            timestamp = datetime.now().isoformat().replace(':', '-').replace('.', '-')
            batch_id = f"batch_{timestamp}_{session_id[:8]}"
            
            saved_ads = []
            storage_paths = []
            
            # Create batch folder path: brands/{uid}/ads-generated/{batch_id}/
            batch_path = f"brands/{brand_id}/ads-generated/{batch_id}"
            
            # Save each ad as a separate JSON file
            for i, ad in enumerate(ads):
                ad_id = f"ad_{i + 1}_{timestamp}"
                ad_filename = f"{ad_id}.json"
                ad_path = f"{batch_path}/{ad_filename}"

                # Process media assets - download and store them
                processed_media_assets = []
                media_assets = ad.get("media_assets", [])

                for j, media_asset in enumerate(media_assets):
                    if isinstance(media_asset, dict) and media_asset.get("url"):
                        media_url = media_asset.get("url")
                        media_type = media_asset.get("type", "image")
                        media_id = media_asset.get("id", f"media_{j}")

                        # Create storage path for media file
                        media_storage_path = f"{batch_path}/media/{ad_id}_{media_id}"

                        # Download and store the media file
                        storage_result = await self._download_and_store_media(
                            media_url,
                            media_storage_path,
                            media_type
                        )

                        if storage_result["success"]:
                            # Update media asset with Firebase Storage URL
                            processed_media_asset = {
                                "id": media_id,
                                "type": media_type,
                                "url": storage_result["public_url"],
                                "storage_path": storage_result["storage_path"],
                                "original_url": storage_result["original_url"],
                                "file_size": storage_result["file_size"],
                                "content_type": storage_result["content_type"],
                                "created_at": media_asset.get("created_at", datetime.now().isoformat()),
                                "metadata": {
                                    **media_asset.get("metadata", {}),
                                    "stored_at": datetime.now().isoformat(),
                                    "storage_bucket": self.bucket.name
                                }
                            }
                            processed_media_assets.append(processed_media_asset)
                            logger.info(f"Processed media asset {media_id} for ad {ad_id}")
                        else:
                            # Keep original URL if storage failed
                            logger.warning(f"Failed to store media asset {media_id}, keeping original URL")
                            processed_media_assets.append(media_asset)
                    else:
                        # Keep non-URL media assets as-is
                        processed_media_assets.append(media_asset)

                # Prepare ad data for storage with processed media assets
                ad_data = {
                    "id": ad_id,
                    "session_id": session_id,
                    "batch_id": batch_id,
                    "platform": ad.get("platform", "unknown"),
                    "headline": ad.get("headline", ""),
                    "description": ad.get("description", ""),
                    "cta": ad.get("cta", ""),
                    "content": ad.get("content", ""),
                    "type": ad.get("type", "text"),
                    "format": ad.get("format", "text"),
                    # New format specifications
                    "format_specs": ad.get("format_specs", {}),
                    "dimensions": ad.get("dimensions", {}),
                    "media_assets": processed_media_assets,
                    "generation_metadata": ad.get("generation_metadata", {}),
                    "platform_adaptations": ad.get("platform_adaptations", {}),
                    "created_at": datetime.now().isoformat(),
                    "brand_id": brand_id,
                    # Version for backward compatibility
                    "storage_version": "2.0"
                }
                
                # Convert to JSON and upload
                ad_json = json.dumps(ad_data, indent=2, ensure_ascii=False)
                
                # Create a temporary file and upload
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                    temp_file.write(ad_json)
                    temp_file_path = temp_file.name
                
                try:
                    # Upload to Firebase Storage
                    blob = self.bucket.blob(ad_path)
                    blob.upload_from_filename(temp_file_path, content_type='application/json')
                    
                    # Make the file publicly readable (optional)
                    # blob.make_public()
                    
                    storage_paths.append(ad_path)
                    saved_ads.append({
                        "ad_id": ad_id,
                        "storage_path": ad_path,
                        "download_url": f"gs://{self.bucket.name}/{ad_path}"
                    })
                    
                    logger.info(f"Saved ad {ad_id} to storage: {ad_path}")
                    
                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
            
            # Save batch metadata
            batch_metadata = {
                "batch_id": batch_id,
                "session_id": session_id,
                "brand_id": brand_id,
                "prompt": metadata.get("prompt", ""),
                "platforms": metadata.get("platforms", []),
                "format": metadata.get("format", ""),
                "format_specs": metadata.get("format_specs", {}),
                "image_style": metadata.get("image_style", ""),
                "ad_type": metadata.get("ad_type", ""),
                "total_ads": len(ads),
                "ad_ids": [ad["ad_id"] for ad in saved_ads],
                "storage_paths": storage_paths,
                "created_at": datetime.now().isoformat(),
                "saved_at": datetime.now().isoformat(),
                "storage_version": "2.0"
            }
            
            metadata_path = f"{batch_path}/metadata.json"
            metadata_json = json.dumps(batch_metadata, indent=2, ensure_ascii=False)
            
            # Save metadata
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                temp_file.write(metadata_json)
                temp_file_path = temp_file.name
            
            try:
                metadata_blob = self.bucket.blob(metadata_path)
                metadata_blob.upload_from_filename(temp_file_path, content_type='application/json')
                
                logger.info(f"Saved batch metadata to storage: {metadata_path}")
                
            finally:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            
            # Also save a reference in Firestore for quick querying
            await self._save_storage_reference(brand_id, batch_id, batch_metadata)
            
            return {
                "success": True,
                "batch_id": batch_id,
                "storage_path": batch_path,
                "saved_ads": saved_ads,
                "metadata_path": metadata_path,
                "total_saved": len(saved_ads)
            }
            
        except Exception as e:
            logger.error(f"Error saving ads to storage: {str(e)}")
            raise Exception(f"Failed to save ads to storage: {str(e)}")
    
    async def get_saved_ads(self, brand_id: str, limit: int = 50) -> Dict[str, Any]:
        """
        Retrieve saved ads for a brand from Firebase Storage
        
        Args:
            brand_id: The brand's UID
            limit: Maximum number of batches to return
            
        Returns:
            Dict containing saved ad batches and metadata
        """
        try:
            # Get storage references from Firestore for quick querying
            db = get_db()
            storage_refs = (
                db.collection("ad_storage_references")
                .where("brand_id", "==", brand_id)
                .limit(limit)
                .stream()
            )
            
            batches = []
            total_ads = 0
            
            for ref_doc in storage_refs:
                ref_data = ref_doc.to_dict()
                batch_id = ref_data.get("batch_id")
                
                try:
                    # Get batch metadata from storage
                    metadata_path = f"brands/{brand_id}/ads-generated/{batch_id}/metadata.json"
                    metadata_blob = self.bucket.blob(metadata_path)
                    
                    if metadata_blob.exists():
                        metadata_content = metadata_blob.download_as_text()
                        batch_metadata = json.loads(metadata_content)
                        
                        # Get individual ads
                        ads = []
                        for ad_id in batch_metadata.get("ad_ids", []):
                            ad_path = f"brands/{brand_id}/ads-generated/{batch_id}/{ad_id}.json"
                            ad_blob = self.bucket.blob(ad_path)
                            
                            if ad_blob.exists():
                                ad_content = ad_blob.download_as_text()
                                ad_data = json.loads(ad_content)

                                # Ensure media assets have accessible URLs
                                if "media_assets" in ad_data:
                                    for media_asset in ad_data["media_assets"]:
                                        if "storage_path" in media_asset:
                                            # Generate a fresh public URL for the stored media
                                            try:
                                                media_blob = self.bucket.blob(media_asset["storage_path"])
                                                if media_blob.exists():
                                                    # Make sure the blob is public and get URL
                                                    media_blob.make_public()
                                                    media_asset["url"] = media_blob.public_url
                                            except Exception as e:
                                                logger.warning(f"Failed to get public URL for media {media_asset.get('id')}: {e}")

                                ads.append(ad_data)
                        
                        batch_info = {
                            **batch_metadata,
                            "ads": ads,
                            "storage_reference_id": ref_doc.id
                        }
                        
                        batches.append(batch_info)
                        total_ads += len(ads)
                        
                except Exception as e:
                    logger.warning(f"Error loading batch {batch_id}: {str(e)}")
                    continue
            
            return {
                "success": True,
                "batches": batches,
                "total_batches": len(batches),
                "total_ads": total_ads
            }
            
        except Exception as e:
            logger.error(f"Error retrieving saved ads: {str(e)}")
            raise Exception(f"Failed to retrieve saved ads: {str(e)}")
    
    async def delete_ad_batch(self, brand_id: str, batch_id: str) -> Dict[str, Any]:
        """
        Delete an ad batch from Firebase Storage
        
        Args:
            brand_id: The brand's UID
            batch_id: The batch ID to delete
            
        Returns:
            Dict containing deletion status
        """
        try:
            batch_path = f"brands/{brand_id}/ads-generated/{batch_id}"
            
            # List all files in the batch folder (including media files)
            blobs = self.bucket.list_blobs(prefix=batch_path)
            deleted_files = []
            deleted_media_count = 0

            for blob in blobs:
                blob.delete()
                deleted_files.append(blob.name)

                # Count media files
                if "/media/" in blob.name:
                    deleted_media_count += 1

                logger.info(f"Deleted file: {blob.name}")

            logger.info(f"Deleted {deleted_media_count} media files from batch {batch_id}")
            
            # Remove Firestore reference
            db = get_db()
            storage_refs = (
                db.collection("ad_storage_references")
                .where("brand_id", "==", brand_id)
                .where("batch_id", "==", batch_id)
                .stream()
            )
            
            for ref_doc in storage_refs:
                ref_doc.reference.delete()
                logger.info(f"Deleted storage reference: {ref_doc.id}")
            
            return {
                "success": True,
                "batch_id": batch_id,
                "deleted_files": deleted_files,
                "total_deleted": len(deleted_files)
            }
            
        except Exception as e:
            logger.error(f"Error deleting ad batch: {str(e)}")
            raise Exception(f"Failed to delete ad batch: {str(e)}")
    
    async def get_storage_stats(self, brand_id: str) -> Dict[str, Any]:
        """
        Get storage usage statistics for a brand
        
        Args:
            brand_id: The brand's UID
            
        Returns:
            Dict containing storage statistics
        """
        try:
            # Get all storage references for the brand
            db = get_db()
            storage_refs = (
                db.collection("ad_storage_references")
                .where("brand_id", "==", brand_id)
                .stream()
            )
            
            total_batches = 0
            total_ads = 0
            total_size = 0
            total_media_files = 0
            
            for ref_doc in storage_refs:
                ref_data = ref_doc.to_dict()
                total_batches += 1
                total_ads += ref_data.get("total_ads", 0)
                
                # Calculate approximate size (this is an estimate)
                batch_id = ref_data.get("batch_id")
                batch_path = f"brands/{brand_id}/ads-generated/{batch_id}"
                
                try:
                    blobs = self.bucket.list_blobs(prefix=batch_path)
                    for blob in blobs:
                        blob.reload()  # Get updated metadata
                        total_size += blob.size or 0

                        # Count media files
                        if "/media/" in blob.name:
                            total_media_files += 1

                except Exception as e:
                    logger.warning(f"Error calculating size for batch {batch_id}: {str(e)}")
            
            return {
                "success": True,
                "total_batches": total_batches,
                "total_ads": total_ads,
                "total_media_files": total_media_files,
                "storage_used_bytes": total_size,
                "storage_used_mb": round(total_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {str(e)}")
            return {
                "success": False,
                "total_batches": 0,
                "total_ads": 0,
                "total_media_files": 0,
                "storage_used_bytes": 0,
                "storage_used_mb": 0
            }
    
    async def _save_storage_reference(self, brand_id: str, batch_id: str, metadata: Dict[str, Any]):
        """Save a reference to the storage batch in Firestore for quick querying"""
        try:
            db = get_db()
            ref_data = {
                "brand_id": brand_id,
                "batch_id": batch_id,
                "prompt": metadata.get("prompt", ""),
                "platforms": metadata.get("platforms", []),
                "total_ads": metadata.get("total_ads", 0),
                "created_at": metadata.get("created_at", datetime.now().isoformat()),
                "storage_path": f"brands/{brand_id}/ads-generated/{batch_id}"
            }
            
            db.collection("ad_storage_references").add(ref_data)
            logger.info(f"Saved storage reference for batch {batch_id}")
            
        except Exception as e:
            logger.warning(f"Failed to save storage reference: {str(e)}")
            # Don't raise exception as this is not critical
