"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  AlertCircle,
  Target,
  BarChart3,
  RefreshCw,
  Download,
  History,
  ArrowLeft,
  Wallet
} from "lucide-react";
import { GEOScoreCard } from "@/components/geo-check/GEOScoreCard";
import { GEORecommendationCard } from "@/components/geo-check/GEORecommendationCard";
import { GEOAnalysisLoader } from "@/components/geo-check/GEOAnalysisLoader";
import { GEOHistoryList } from "@/components/geo-check/GEOHistoryList";
import { GEOHistoricalReport } from "@/components/geo-check/GEOHistoricalReport";
import { centsToDollars } from "@/lib/utils";

interface PageAnalysis {
  url: string;
  title: string;
  summary: string;
  score: number;
}

interface GEOAnalysis {
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: PageAnalysis[];
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: {
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }[];
  brandId: string;
}

interface GEOHistoryItem {
  id: string;
  created_at: string;
  overall_score: number;
  website_analyzed: string;
  brand_name: string;
  analyzed_pages_count: number;
  total_queries_simulated: number;
  prompt_mention_rate: number;
  citation_rate: number;
  website_optimization: number;
  sentiment_tone: number;
  analysis_version: string;
}

interface HistoricalGEOAnalysis {
  analysisId: string;
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: Array<{
    url: string;
    title: string;
    summary: string;
    score: number;
  }>;
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: Array<{
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }>;
  analyzedAt: string;
  brandId: string;
  websiteAnalyzed: string;
  brandName: string;
  isHistorical: boolean;
  analysisVersion: string;
}

export default function GEOCheckPage() {
  const { user } = useAuth();
  const [analysis, setAnalysis] = useState<GEOAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [brandData, setBrandData] = useState<{
    website?: string;
    company_name?: string;
    industry?: string;
  } | null>(null);
  const [walletBalance, setWalletBalance] = useState<number>(0);

  // History state management
  const [currentView, setCurrentView] = useState<'analysis' | 'history' | 'historical-report'>('analysis');
  const [history, setHistory] = useState<GEOHistoryItem[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [selectedHistoricalAnalysis, setSelectedHistoricalAnalysis] = useState<HistoricalGEOAnalysis | null>(null);
  const [historyPagination, setHistoryPagination] = useState({
    offset: 0,
    limit: 5,
    hasMore: false,
    total: 0
  });

  const fetchBrandData = useCallback(async () => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/profile`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBrandData(data);
      }
    } catch (error) {
      console.error("Error fetching brand data:", error);
    }
  }, [user]);

  const fetchWalletBalance = useCallback(async () => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        // Convert from cents to dollars
        setWalletBalance(centsToDollars(data.wallet_balance || 0));
      }
    } catch (error) {
      console.error("Error fetching wallet balance:", error);
    }
  }, [user]);

  useEffect(() => {
    fetchBrandData();
    fetchWalletBalance();
  }, [fetchBrandData, fetchWalletBalance]);

  // Fetch GEO analysis history
  const fetchHistory = useCallback(async (loadMore = false) => {
    if (!user) return;

    setHistoryLoading(true);
    try {
      const token = await user.getIdToken();
      const offset = loadMore ? historyPagination.offset + historyPagination.limit : 0;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/history?limit=${historyPagination.limit}&offset=${offset}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();

        if (loadMore) {
          setHistory(prev => [...prev, ...data.history]);
        } else {
          setHistory(data.history);
        }

        setHistoryPagination({
          offset: offset,
          limit: historyPagination.limit,
          hasMore: data.pagination.has_more,
          total: data.pagination.total
        });
      } else {
        console.error("Failed to fetch history");
      }
    } catch (error) {
      console.error("Error fetching history:", error);
    } finally {
      setHistoryLoading(false);
    }
  }, [user, historyPagination.limit, historyPagination.offset]);

  // Fetch specific historical analysis
  const fetchHistoricalAnalysis = useCallback(async (analysisId: string) => {
    if (!user) return;

    setLoading(true);
    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/history/${analysisId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setSelectedHistoricalAnalysis(data);
        setCurrentView('historical-report');
      } else {
        const errorData = await response.json().catch(() => ({}));
        alert(`Failed to load historical analysis: ${errorData.detail || 'Please try again.'}`);
      }
    } catch (error) {
      console.error("Error fetching historical analysis:", error);
      alert("Failed to load historical analysis. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load history when component mounts or switching to history view
  useEffect(() => {
    if ((currentView === 'history' || currentView === 'analysis') && history.length === 0) {
      fetchHistory();
    }
  }, [currentView, history.length, fetchHistory]);

  const runGEOReport = async () => {
    if (!brandData?.website) {
      alert("Please add your website in the brand profile first.");
      return;
    }

    setLoading(true);
    try {
      const token = await user?.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/check`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          brand_id: null // Will use authenticated user's brand
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysis(data);
        setCurrentView('analysis');

        // Refresh history to include the new analysis
        if (history.length > 0) {
          fetchHistory();
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to run GEO analysis");
      }
    } catch (error) {
      console.error("Error running GEO analysis:", error);
      alert(`Failed to run GEO analysis: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const exportToPDF = () => {
    if (!analysis) return;

    // Create a simple HTML report for PDF generation
    const reportContent = `
      <html>
        <head>
          <title>GEO Analysis Report - ${brandData?.company_name || 'Brand'}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              margin: 20px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 40px;
              padding-bottom: 20px;
              border-bottom: 2px solid #e5e7eb;
            }
            .score {
              font-size: 28px;
              font-weight: bold;
              color: #2563eb;
              margin-top: 15px;
            }
            .section {
              margin-bottom: 30px;
              page-break-inside: avoid;
            }
            .section h3 {
              color: #1f2937;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 8px;
              margin-bottom: 15px;
            }
            .metric {
              display: inline-block;
              margin: 8px;
              padding: 12px;
              border: 1px solid #d1d5db;
              border-radius: 8px;
              background: #f9fafb;
              min-width: 200px;
            }
            .recommendation {
              margin: 15px 0;
              padding: 15px;
              border-left: 4px solid #2563eb;
              background: #f8fafc;
              border-radius: 6px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .page {
              margin: 12px 0;
              padding: 12px;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              background: #fafafa;
            }
            @media print {
              .section { page-break-inside: avoid; }
              .recommendation { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>GEO Report</h1>
            <h2>${brandData?.company_name || 'Brand'}</h2>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
            <div class="score">Overall GEO Score: ${analysis.overallScore}/100</div>
          </div>

          <div class="section">
            <h3>Score Breakdown</h3>
            <div class="metric">Prompt Mention Rate: ${Math.round(analysis.promptMentionRate)}% (40% weight)</div>
            <div class="metric">Citation Rate: ${Math.round(analysis.citationRate)}% (20% weight)</div>
            <div class="metric">Website Optimization: ${analysis.websiteOptimization}/100 (30% weight)</div>
            <div class="metric">Sentiment/Tone: ${analysis.sentimentTone}/100 (10% weight)</div>
          </div>

          <div class="section">
            <h3>Analyzed Pages (${analysis.analyzedPages.length})</h3>
            ${analysis.analyzedPages.map(page => `
              <div class="page">
                <strong>${page.title}</strong> (Score: ${page.score}/100)<br>
                <small>${page.url}</small><br>
                ${page.summary}
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h3>Actionable GEO Recommendations</h3>
            <p style="margin-bottom: 15px; color: #666; font-style: italic;">
              These AI-powered recommendations are specifically tailored to improve your brand's visibility in AI-generated responses.
            </p>
            ${analysis.recommendations.map((rec, index) => `
              <div class="recommendation" style="margin-bottom: 20px; page-break-inside: avoid;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <span style="background: ${rec.priority === 'high' ? '#dc2626' : rec.priority === 'medium' ? '#d97706' : '#16a34a'};
                               color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; margin-right: 10px;">
                    ${rec.priority.toUpperCase()}
                  </span>
                  <span style="background: #f3f4f6; padding: 2px 8px; border-radius: 12px; font-size: 12px; color: #374151;">
                    ${rec.category}
                  </span>
                </div>
                <h4 style="margin: 8px 0; font-size: 16px; color: #1f2937;">${index + 1}. ${rec.title}</h4>
                <p style="margin: 8px 0; line-height: 1.5; color: #4b5563;">${rec.description}</p>
                <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; border-left: 4px solid #0ea5e9; margin-top: 10px;">
                  <strong style="color: #0c4a6e;">Expected Impact:</strong> ${rec.impact}
                </div>
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h3>AI Query Simulation</h3>
            ${analysis.simulatedQueries.map(query => `
              <div class="page">
                <strong>"${query.query}"</strong><br>
                Brand Mentioned: ${query.brand_mentioned ? 'Yes' : 'No'}<br>
                ${query.mention_context ? `Context: ${query.mention_context}<br>` : ''}
                Likelihood Score: ${query.likelihood_score}/100<br>
                ${query.reasoning}
              </div>
            `).join('')}
          </div>
        </body>
      </html>
    `;

    // Create a new window and print
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(reportContent);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-slate-200 dark:border-gray-700 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex items-start gap-4">
              {currentView === 'historical-report' && (
                <Button
                  onClick={() => setCurrentView('history')}
                  variant="outline"
                  size="sm"
                  className={`gap-2 shrink-0 ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={loading}
                  title={loading ? 'Navigation disabled during analysis' : ''}
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to History
                </Button>
              )}

              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tight text-slate-900 dark:text-white">
                  {currentView === 'history' ? 'GEO Report History' :
                   currentView === 'historical-report' ? 'Historical GEO Report' : 'GEO Report History'}
                </h1>
                <h2 className="text-lg text-slate-600 dark:text-gray-300 font-medium">
                  {currentView === 'analysis' && 'Track Your AI Optimization Performance'}
                </h2>
                <p className="text-slate-500 dark:text-gray-400 text-sm leading-relaxed max-w-3xl">
                  {currentView === 'history' ? 'View and compare your past GEO reports to track improvements over time' :
                   currentView === 'historical-report' ? 'Detailed view of historical analysis with comprehensive insights' :
                   'View and compare your past GEO reports to track improvements over time. Monitor how your brand appears in AI-generated responses and measure the impact of your optimization efforts.'}
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              {/* Wallet Balance Display */}
              <div className="flex items-center gap-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg px-4 py-2">
                <Wallet className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <div>
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200">Wallet Balance</p>
                  <p className="text-lg font-semibold text-blue-900 dark:text-blue-100">${walletBalance.toFixed(2)}</p>
                </div>
              </div>

              {/* Export button for current analysis */}
              {analysis && currentView === 'analysis' && (
                <Button
                  onClick={exportToPDF}
                  variant="outline"
                  className="gap-2 min-w-[120px]"
                >
                  <Download className="h-4 w-4" />
                  Export PDF
                </Button>
              )}

              {/* Historical report actions */}
              {currentView === 'historical-report' && selectedHistoricalAnalysis && (
                <>
                  <Button
                    onClick={() => {
                      // Export historical report to PDF
                      const reportContent = `
                        <html>
                          <head>
                            <title>Historical GEO Analysis Report - ${selectedHistoricalAnalysis.brandName}</title>
                            <style>
                              body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; line-height: 1.6; color: #333; }
                              .header { text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 2px solid #e5e7eb; }
                              .score { font-size: 28px; font-weight: bold; color: #2563eb; margin-top: 15px; }
                            </style>
                          </head>
                          <body>
                            <div class="header">
                              <h1>Historical GEO Analysis Report</h1>
                              <h2>${selectedHistoricalAnalysis.brandName}</h2>
                              <p>Analysis Date: ${new Date(selectedHistoricalAnalysis.analyzedAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' })}</p>
                              <p>Website: ${selectedHistoricalAnalysis.websiteAnalyzed}</p>
                              <div class="score">Overall GEO Score: ${selectedHistoricalAnalysis.overallScore}/100</div>
                            </div>
                          </body>
                        </html>
                      `;
                      const printWindow = window.open('', '_blank');
                      if (printWindow) {
                        printWindow.document.write(reportContent);
                        printWindow.document.close();
                        printWindow.focus();
                        setTimeout(() => {
                          printWindow.print();
                          printWindow.close();
                        }, 250);
                      }
                    }}
                    variant="outline"
                    className="gap-2 min-w-[120px]"
                  >
                    <Download className="h-4 w-4" />
                    Export PDF
                  </Button>
                  <Button
                    onClick={runGEOReport}
                    disabled={loading || !brandData?.website}
                    className="gap-2 min-w-[140px] bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                  >
                    {loading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Search className="h-4 w-4" />
                    )}
                    {loading ? "Analyzing..." : "Run New Analysis"}
                  </Button>
                </>
              )}

              {/* Run analysis button */}
              {currentView !== 'historical-report' && (
                <Button
                  onClick={runGEOReport}
                  disabled={loading || !brandData?.website}
                  className="gap-2 min-w-[140px] bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  {loading ? "Analyzing..." : "Run GEO Report"}
                </Button>
              )}
            </div>
          </div>
        </div>
        {/* Content Area */}
        <div className="space-y-6">
          {/* Historical Report View */}
          {currentView === 'historical-report' && selectedHistoricalAnalysis && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-slate-200 dark:border-gray-700">
              <GEOHistoricalReport
                analysis={selectedHistoricalAnalysis}
              />
            </div>
          )}

          {/* History View */}
          {currentView === 'history' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-slate-200 dark:border-gray-700">
              <GEOHistoryList
                history={history}
                loading={historyLoading}
                onViewReport={fetchHistoricalAnalysis}
                onLoadMore={() => fetchHistory(true)}
                hasMore={historyPagination.hasMore}
                currentAnalysis={analysis}
              />
            </div>
          )}

          {/* Analysis View */}
          {currentView === 'analysis' && (
            <>
              {/* Loading State */}
              {loading && (
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-slate-200 dark:border-gray-700 p-6">
                  <GEOAnalysisLoader
                    isLoading={loading}
                    brandName={brandData?.company_name}
                    website={brandData?.website}
                  />
                </div>
              )}

              {!analysis && !loading && (
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-slate-200 dark:border-gray-700">
                  <GEOHistoryList
                    history={history}
                    loading={historyLoading}
                    onViewReport={fetchHistoricalAnalysis}
                    onLoadMore={() => fetchHistory(true)}
                    hasMore={historyPagination.hasMore}
                    currentAnalysis={analysis}
                    onRunNewAnalysis={runGEOReport}
                    brandData={brandData}
                  />
                </div>
              )}
            </>
          )}

          {/* Report Results - Only show in analysis view */}
          {currentView === 'analysis' && analysis && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-slate-200 dark:border-gray-700 p-6">
              <Tabs defaultValue="overview" className="space-y-8">
                <div className="border-b border-slate-200 dark:border-gray-700 pb-4">
                  <TabsList className="grid w-full grid-cols-6 bg-slate-50 dark:bg-gray-700 p-1 rounded-lg">
                    <TabsTrigger value="overview" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Overview</TabsTrigger>
                    <TabsTrigger value="pages" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Pages Analyzed</TabsTrigger>
                    <TabsTrigger value="queries" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">AI Queries</TabsTrigger>
                    <TabsTrigger value="discoverability" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">AI Discoverability</TabsTrigger>
                    <TabsTrigger value="content" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Content Report</TabsTrigger>
                    <TabsTrigger value="recommendations" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Recommendations</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="overview" className="space-y-8 mt-8">
                  {/* Overall Score */}
                  <Card className="border-slate-200 shadow-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3 text-xl">
                        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-2">
                          <BarChart3 className="h-6 w-6 text-blue-600" />
                        </div>
                        Overall GEO Score
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex items-center gap-6">
                        <div className="text-5xl font-bold">
                          <span className={getScoreColor(analysis.overallScore)}>
                            {analysis.overallScore}
                          </span>
                          <span className="text-slate-400 text-2xl">/100</span>
                        </div>
                        <div className="flex-1 space-y-2">
                          <Progress value={analysis.overallScore} className="h-4" />
                          <p className="text-sm text-slate-500">
                            Your brand&apos;s overall optimization for AI search engines
                          </p>
                        </div>
                        <Badge
                          variant={getScoreBadgeVariant(analysis.overallScore)}
                          className="px-4 py-2 text-sm font-medium"
                        >
                          {analysis.overallScore >= 80 ? "Excellent" :
                           analysis.overallScore >= 60 ? "Good" : "Needs Improvement"}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Weighted Score Components */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900 mb-4">Core GEO Metrics</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <GEOScoreCard
                          title="Prompt Mention Rate"
                          score={Math.round(analysis.promptMentionRate)}
                          description="40% weight - Brand appears in AI responses"
                        />
                        <GEOScoreCard
                          title="Citation Rate"
                          score={Math.round(analysis.citationRate)}
                          description="20% weight - Website links in AI outputs"
                        />
                        <GEOScoreCard
                          title="Website Optimization"
                          score={analysis.websiteOptimization}
                          description="30% weight - Content quality and structure"
                        />
                        <GEOScoreCard
                          title="Sentiment/Tone"
                          score={analysis.sentimentTone}
                          description="10% weight - Positive brand framing"
                        />
                      </div>
                    </div>

                    {/* Additional Metrics */}
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900 mb-4">Additional Insights</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <GEOScoreCard
                          title="AI Discoverability"
                          score={analysis.aiDiscoverability.score}
                          description={`${analysis.aiDiscoverability.mentions} mentions found`}
                        />
                        <GEOScoreCard
                          title="Content Optimization"
                          score={analysis.contentOptimization.score}
                          description="AI-friendly structure"
                        />
                        <GEOScoreCard
                          title="Share of Voice"
                          score={analysis.competitiveAnalysis.shareOfVoice}
                          maxScore={100}
                          description="vs competitors"
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="pages" className="space-y-6 mt-8">
                  <Card className="border-slate-200 shadow-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl">
                        Analyzed Pages ({analysis.analyzedPages.length})
                      </CardTitle>
                      <p className="text-sm text-slate-500">
                        Individual page analysis and optimization scores
                      </p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        {analysis.analyzedPages.map((page, index) => (
                          <div key={index} className="border border-slate-200 rounded-lg p-6 space-y-3 hover:shadow-sm transition-shadow">
                            <div className="flex items-start justify-between gap-4">
                              <h4 className="font-semibold text-slate-900 leading-tight">{page.title}</h4>
                              <Badge
                                variant={page.score >= 70 ? "default" : page.score >= 50 ? "secondary" : "destructive"}
                                className="shrink-0 px-3 py-1"
                              >
                                {page.score}/100
                              </Badge>
                            </div>
                            <p className="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                              {page.url}
                            </p>
                            <p className="text-slate-600 leading-relaxed">{page.summary}</p>
                          </div>
                        ))}
                        {analysis.analyzedPages.length === 0 && (
                          <div className="text-center py-12">
                            <div className="bg-slate-50 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                              <AlertCircle className="h-8 w-8 text-slate-400" />
                            </div>
                            <p className="text-slate-500 font-medium">No pages were analyzed</p>
                            <p className="text-sm text-slate-400 mt-1">
                              This might indicate issues with sitemap discovery or page accessibility.
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="queries" className="space-y-6 mt-8">
                  <Card className="border-slate-200 shadow-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl">AI Query Simulation Results</CardTitle>
                      <p className="text-sm text-slate-500">
                        How your brand performs in simulated AI assistant queries
                      </p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        {analysis.simulatedQueries.map((query, index) => (
                          <div key={index} className="border border-slate-200 rounded-lg p-6 space-y-4 hover:shadow-sm transition-shadow">
                            <div className="flex items-start justify-between gap-4">
                              <h4 className="font-semibold text-slate-900 leading-tight">
                                &quot;{query.query}&quot;
                              </h4>
                              <div className="flex items-center gap-2 shrink-0">
                                <Badge variant={query.brand_mentioned ? "default" : "outline"}>
                                  {query.brand_mentioned ? "Mentioned" : "Not Mentioned"}
                                </Badge>
                                {query.mention_context && (
                                  <Badge variant={
                                    query.mention_context === "positive" ? "default" :
                                    query.mention_context === "neutral" ? "secondary" : "destructive"
                                  }>
                                    {query.mention_context}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className="text-sm text-slate-500">
                              Likelihood Score: <span className="font-medium">{query.likelihood_score}/100</span>
                            </div>
                            <p className="text-slate-600 leading-relaxed">{query.reasoning}</p>
                          </div>
                        ))}
                        {analysis.simulatedQueries.length === 0 && (
                          <div className="text-center py-12">
                            <div className="bg-slate-50 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                              <AlertCircle className="h-8 w-8 text-slate-400" />
                            </div>
                            <p className="text-slate-500 font-medium">No queries were simulated</p>
                            <p className="text-sm text-slate-400 mt-1">
                              Please try running the analysis again.
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="discoverability" className="space-y-6 mt-8">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="border-slate-200 shadow-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-xl">
                          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-2">
                            <Target className="h-6 w-6 text-green-600" />
                          </div>
                          AI Mention Report
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6 pt-0">
                        <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                          <span className="font-medium text-slate-700">Total Mentions</span>
                          <Badge variant="outline" className="px-3 py-1 text-lg font-semibold">
                            {analysis.aiDiscoverability.mentions}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                          <span className="font-medium text-slate-700">Sentiment</span>
                          <Badge variant={
                            analysis.aiDiscoverability.sentiment === "positive" ? "default" :
                            analysis.aiDiscoverability.sentiment === "neutral" ? "secondary" : "destructive"
                          } className="px-3 py-1 capitalize">
                            {analysis.aiDiscoverability.sentiment}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-slate-200 shadow-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl">Top Queries</CardTitle>
                        <p className="text-sm text-slate-500">
                          Most relevant queries where your brand appears
                        </p>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          {analysis.aiDiscoverability.topQueries.map((query, index) => (
                            <div key={index} className="text-sm p-4 bg-slate-50 rounded-lg border border-slate-200">
                              &quot;{query}&quot;
                            </div>
                          ))}
                          {analysis.aiDiscoverability.topQueries.length === 0 && (
                            <p className="text-slate-400 text-center py-4">No top queries available</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="content" className="space-y-6 mt-8">
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900 mb-6">Content Optimization Breakdown</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <GEOScoreCard
                        title="Structure Score"
                        score={analysis.contentOptimization.structureScore}
                        description="Heading hierarchy and organization"
                      />
                      <GEOScoreCard
                        title="Factual Claims"
                        score={analysis.contentOptimization.factualClaimsScore}
                        description="Evidence-based content"
                      />
                      <GEOScoreCard
                        title="AI Readability"
                        score={analysis.contentOptimization.aiReadabilityScore}
                        description="Optimized for AI consumption"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="recommendations" className="space-y-6 mt-8">
                  <div className="space-y-4">
                    {analysis.recommendations.map((rec, index) => (
                      <GEORecommendationCard
                        key={index}
                        recommendation={rec}
                      />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
