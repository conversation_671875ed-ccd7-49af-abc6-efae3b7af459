"use client";

import { Suspense, useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Mail, Lock, Eye, EyeOff, User, Globe, Loader2 } from "lucide-react";
import { FcGoogle } from "react-icons/fc";
import {
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithPopup,
  createUserWithEmailAndPassword,
} from "firebase/auth";
import { auth } from "@/lib/firebase";
import { handleRegistrationFailureCleanup } from "@/lib/firebase-utils";
import Link from "next/link";
import { extractWebsiteSlug, normalizeWebsiteUrl, getDomainValidationError } from "@/lib/utils";
import { useAnalytics } from "@/components/AnalyticsProvider";
import { TrackedButton } from "@/components/ui/tracked-button";

// Helper function to get user-friendly error messages from Firebase auth errors
function getFirebaseErrorMessage(error: any): string {
  const errorCode = error?.code || '';
  const errorMessage = error?.message || '';

  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address. Please check your email or sign up for a new account.';
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again or reset your password.';
    case 'auth/invalid-email':
      return 'Please enter a valid email address.';
    case 'auth/user-disabled':
      return 'This account has been disabled. Please contact support for assistance.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later or reset your password.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your internet connection and try again.';
    case 'auth/invalid-credential':
      return 'Invalid email or password. Please check your credentials and try again.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists. Please sign in instead.';
    case 'auth/weak-password':
      return 'Password is too weak. Please choose a stronger password.';
    case 'auth/popup-closed-by-user':
      return 'Sign-in was cancelled. Please try again.';
    case 'auth/popup-blocked':
      return 'Pop-up was blocked by your browser. Please allow pop-ups and try again.';
    case 'auth/cancelled-popup-request':
      return 'Sign-in was cancelled. Please try again.';
    default:
      // For unknown errors, return a generic message but log the original for debugging
      console.error('Firebase Auth Error:', errorCode, errorMessage);
      return 'An error occurred during sign-in. Please try again.';
  }
}

function SignInContent() {
  const searchParams = useSearchParams();
  const redirect = searchParams?.get("redirect") || "/dashboard";
  const role = searchParams?.get("role") as "brand" | "agent" | null;
  const mode = searchParams?.get("mode") as "signin" | "signup" | null;
  const type = searchParams?.get("type") as "sign" | "get-started" | null;

  return <SignInForm redirect={redirect} role={role} mode={mode} type={type} />;
}

export default function SignInPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      }
    >
      <SignInContent />
    </Suspense>
  );
}

function SignInForm({
  redirect,
  role,
  mode,
  type,
}: {
  redirect: string;
  role: "brand" | "agent" | null;
  mode: "signin" | "signup" | null;
  type: "sign" | "get-started" | null;
}) {
  const router = useRouter();
  const analytics = useAnalytics();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSignUp, setIsSignUp] = useState(mode === "signup" || type === "get-started" || false);
  const [name, setName] = useState("");
  const [website, setWebsite] = useState("");
  const [emailUsername, setEmailUsername] = useState("");
  const [websiteDomain, setWebsiteDomain] = useState("");
  const [selectedRole, setSelectedRole] = useState<"brand" | "agent">(
    (role as "brand" | "agent") || "brand"
  );
  const [isCheckingWebsite, setIsCheckingWebsite] = useState(false);
  const [isCheckingAgentName, setIsCheckingAgentName] = useState(false);

  const googleProvider = new GoogleAuthProvider();

  // Effect to handle URL parameter changes and track page view
  useEffect(() => {
    if (mode === "signup" || type === "get-started") {
      setIsSignUp(true);
    } else if (mode === "signin" || type === "sign") {
      setIsSignUp(false);
    }

    // Track auth page view
    analytics.track({
      action: 'auth_page_view',
      category: 'authentication',
      label: isSignUp ? 'signup' : 'signin',
      custom_parameters: {
        role: selectedRole,
        mode: mode,
        type: type,
        redirect_path: redirect
      }
    });
  }, [mode, type, analytics, isSignUp, selectedRole, redirect]);

  const handleSuccess = () => {
    // Track successful authentication
    analytics.trackLogin(isSignUp ? 'email_signup' : 'email_signin');
    analytics.track({
      action: isSignUp ? 'signup_success' : 'signin_success',
      category: 'authentication',
      label: selectedRole,
      custom_parameters: {
        method: 'email',
        role: selectedRole,
        redirect_path: redirect
      }
    });
    router.push(redirect);
  };

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Get ID token and call login endpoint to sync with backend
      try {
        const idToken = await user.getIdToken();
        const loginResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/email-login`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${idToken}`,
            "Content-Type": "application/json",
          },
        });

        if (!loginResponse.ok) {
          const errorData = await loginResponse.json();
          console.error("Backend login failed:", errorData);
          // Don't fail the sign-in for backend sync issues, just log them
        }
      } catch (backendError) {
        console.error("Error syncing with backend:", backendError);
        // Continue with sign-in even if backend sync fails
      }

      handleSuccess();
    } catch (err) {
      const errorMessage = getFirebaseErrorMessage(err);
      setError(errorMessage);
      console.error("Sign-in error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError("");

    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;

      // Get ID token and call Google onboard endpoint
      try {
        const idToken = await user.getIdToken();
        const onboardResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/google-onboard`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${idToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ role: selectedRole || "user" }),
        });

        if (!onboardResponse.ok) {
          const errorData = await onboardResponse.json();
          console.error("Google onboard failed:", errorData);
          // Don't fail the sign-in for backend sync issues, just log them
        }
      } catch (backendError) {
        console.error("Error syncing Google sign-in with backend:", backendError);
        // Continue with sign-in even if backend sync fails
      }

      handleSuccess();
    } catch (err) {
      const errorMessage = getFirebaseErrorMessage(err);
      setError(errorMessage);
      console.error("Google sign-in error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const extractDomainFromWebsite = (websiteUrl: string): string => {
    try {
      let url = websiteUrl.trim();
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        url = "https://" + url;
      }
      const domain = new URL(url).hostname.toLowerCase();
      return domain.startsWith("www.") ? domain.substring(4) : domain;
    } catch {
      return "";
    }
  };

  const handleWebsiteChange = (value: string) => {
    setWebsite(value);
    setError("");

    const domain = extractDomainFromWebsite(value);
    setWebsiteDomain(domain);
    if (emailUsername && domain) {
      setEmail(`${emailUsername}@${domain}`);
    }
  };

  const handleEmailUsernameChange = (value: string) => {
    setEmailUsername(value);
    if (value && websiteDomain) {
      setEmail(`${value}@${websiteDomain}`);
    }
  };

  const handleBack = () => {
    router.push('/');
  };

  const handleGetStarted = () => {
    setIsSignUp(true);
    setError("");
    // Update URL to reflect the change
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('mode', 'signup');
    window.history.replaceState({}, '', newUrl.toString());
  };

  const handleSignInClick = () => {
    setIsSignUp(false);
    setError("");
    // Update URL to reflect the change
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('mode', 'signin');
    window.history.replaceState({}, '', newUrl.toString());
  };

  const checkWebsiteExists = async (website: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/check-website-exists`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          website: normalizeWebsiteUrl(website), // Normalize URL before checking
        }),
      });

      if (!response.ok) {
        // Handle different error status codes with specific messages
        console.log("Response not ok:", response.status);
        if (response.status === 400) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || "Invalid website URL format. Please check your website URL and try again.");
        } else if (response.status === 500) {
          throw new Error("Server error while checking website. Please try again in a moment.");
        } else if (response.status >= 500) {
          throw new Error("Service temporarily unavailable. Please try again later.");
        } else {
          throw new Error("Unable to verify website. Please check your internet connection and try again.");
        }
      }

      return await response.json();
    } catch (error) {
      console.error("Error checking website existence:", error);
      // Re-throw the error with the specific message, or provide a fallback
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("Network error. Please check your internet connection and try again.");
      }
    }
  };

  const checkAgentNameExists = async (name: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/check-agent-name-exists`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: name.trim(),
        }),
      });

      if (!response.ok) {
        // Handle different error status codes with specific messages
        if (response.status === 400) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || "Invalid agent name format. Please use only letters, numbers, and spaces.");
        } else if (response.status === 500) {
          throw new Error("Server error while checking agent name. Please try again in a moment.");
        } else if (response.status >= 500) {
          throw new Error("Service temporarily unavailable. Please try again later.");
        } else {
          throw new Error("Unable to verify agent name. Please check your internet connection and try again.");
        }
      }

      return await response.json();
    } catch (error) {
      console.error("Error checking agent name existence:", error);
      // Re-throw the error with the specific message, or provide a fallback
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("Network error. Please check your internet connection and try again.");
      }
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    let firebaseUser: any = null;

    try {
      // Step 1: Validate all required fields first
      if (!password.trim()) {
        throw new Error("Please enter a password to create your account.");
      }

      if (selectedRole === "brand") {
        if (!website.trim()) {
          throw new Error("Please enter your company website to continue.");
        }
        if (!emailUsername.trim()) {
          throw new Error("Please enter your email username to continue.");
        }
        if (!email.trim()) {
          throw new Error("Please enter your email address to continue.");
        }

        // Validate domain match
        const domainError = getDomainValidationError(email, website);
        if (domainError) {
          throw new Error(domainError);
        }

        // Step 2: Check if website already exists BEFORE creating Firebase user
        setIsCheckingWebsite(true);
        try {
          const websiteCheck = await checkWebsiteExists(website);
          if (websiteCheck.exists) {
            throw new Error("This website is already registered with AdMesh. Please use a different website or sign in to your existing account.");
          }
        } finally {
          setIsCheckingWebsite(false);
        }
      } else if (selectedRole === "agent") {
        // Agent validation
        if (!name.trim()) {
          throw new Error("Please enter your name to create your agent profile.");
        }
        if (!email.trim()) {
          throw new Error("Please enter your email address to continue.");
        }

        // Step 2: Check if agent name already exists BEFORE creating Firebase user
        setIsCheckingAgentName(true);
        try {
          const agentCheck = await checkAgentNameExists(name);
          if (agentCheck.exists) {
            throw new Error("This agent name is already taken. Please choose a different name for your profile.");
          }
        } finally {
          setIsCheckingAgentName(false);
        }
      } else {
        // User validation
        if (!name.trim()) {
          throw new Error("Please enter your name to create your account.");
        }
        if (!email.trim()) {
          throw new Error("Please enter your email address to continue.");
        }
      }

      // Step 3: Only create Firebase user AFTER all validations pass
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      firebaseUser = userCredential.user;

      // Step 4: Get ID token for API calls
      const idToken = await firebaseUser.getIdToken();

      // Step 5: Register user with backend
      const registerResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/email-register`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${idToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          role: selectedRole,
          name: selectedRole === "agent" ? name : undefined,
          website: selectedRole === "brand" ? normalizeWebsiteUrl(website) : undefined,
        }),
      });

      if (!registerResponse.ok) {
        const errorData = await registerResponse.json();
        const backendError = new Error(errorData.detail || "Registration failed");

        // Backend registration failed - clean up Firebase user
        await handleRegistrationFailureCleanup(firebaseUser, backendError);

        throw backendError;
      }

      handleSuccess();
    } catch (err) {
      let errorMessage: string;

      // Check if it's a Firebase auth error
      if (err && typeof err === 'object' && 'code' in err) {
        errorMessage = getFirebaseErrorMessage(err);

        // If Firebase user creation failed, no cleanup needed
        // If it's an auth/email-already-in-use error, don't attempt cleanup
      } else {
        errorMessage = (err as Error).message;

        // For non-Firebase errors that occur after user creation,
        // cleanup should have already been handled above
      }

      setError(errorMessage);
      console.error("Sign-up error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Simple Back Button */}
      <div className="absolute top-4 left-4">
        <button
          onClick={handleBack}
          className="p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          aria-label="Go back"
        >
          <svg
            className="h-5 w-5 text-gray-700 dark:text-gray-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
      </div>

      <div className="flex flex-1">
        {/* Left127.0.0.1 side - Image */}
        <div className="hidden lg:flex w-1/2 bg-black items-center justify-center p-12">
          <div className="max-w-md text-center">
            <h2 className="text-4xl font-bold mb-6 text-white">
              Welcome to AdMesh
            </h2>
            <p className="text-lg mb-8 text-blue-100">
              {isSignUp
                ? "Join our platform to connect with customers through AI agents"
                : "Sign in to access your account and manage your offers"}
            </p>
            <div className="mt-8 bg-black rounded-lg p-6 text-left text-white border border-gray-800">
              {selectedRole === "brand" && (
                <>
                  <div className="mb-4">
                    <p className="italic text-white">
                      &quot;AdMesh gave us instant visibility across AI apps. We
                      hit 500 conversions in our first week through agent-driven
                      offers. It&apos;s the future of marketing.&quot;
                    </p>
                  </div>
                  <div className="text-sm">
                    <p className="font-semibold text-white">Samantha G.</p>
                    <p className="text-gray-300">Head of Growth, Blinko CRM</p>
                  </div>
                </>
              )}

              {selectedRole === "agent" && (
                <>
                  <div className="mb-4">
                    <p className="italic text-white">
                      &quot;The quality of leads from AdMesh is unmatched.
                      We&apos;ve seen a 300% increase in qualified demos since
                      integrating our offers. Highly recommended!&quot;
                    </p>
                  </div>
                  <div className="text-sm">
                    <p className="font-semibold text-white">Kai T.</p>
                    <p className="text-gray-300">Creator of AskAIAgent</p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Right side - Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md space-y-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {isSignUp ? "Get started with AdMesh" : "Welcome back"}
              </h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {isSignUp
                  ? "Create your account to start connecting with customers through AI agents"
                  : "Sign in to your account to continue"}
              </p>
            </div>

            {isSignUp && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  I am a...
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { id: "brand", label: "Advertiser" },
                    { id: "agent", label: "AI Platform" },
                  ].map((role) => (
                    <button
                      key={role.id}
                      type="button"
                      onClick={() =>
                        setSelectedRole(role.id as "brand" | "agent")
                      }
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        selectedRole === role.id
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                      }`}
                    >
                      {role.label}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {!isSignUp ? (
              // Sign In Form
              <form onSubmit={handleEmailSignIn} className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      Email
                    </label>
                    <div className="relative">
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10 h-12"
                        required
                      />
                      <Mail className="absolute left-3 top-3.5 text-gray-400" />
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <label
                        htmlFor="password"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Password
                      </label>
                      <Link
                        href="/auth/forgot-password"
                        className="text-sm text-primary hover:text-primary/80 hover:underline"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="••••••••"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="pl-10 h-12 pr-10"
                        required
                      />
                      <Lock className="absolute left-3 top-3.5 text-gray-400" />
                      <button
                        type="button"
                        className="absolute right-3 top-3.5 text-gray-400 hover:text-gray-500"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff
                            className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 cursor-pointer text-muted-foreground"
                            onClick={() => setShowPassword(false)}
                          />
                        ) : (
                          <Eye
                            className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 cursor-pointer text-muted-foreground"
                            onClick={() => setShowPassword(true)}
                          />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg p-3 text-sm">
                    {error}
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full h-12"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    "Sign in"
                  )}
                </Button>

                {/* <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-400">
                      Or continue with
                    </span>
                  </div>
                </div> */}

                {/* <Button
                  type="button"
                  variant="outline"
                  className="w-full h-12 flex items-center justify-center gap-2"
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
                >
                  <FcGoogle className="h-5 w-5" />
                  <span>Google</span>
                </Button> */}

                <p className="mt-2 text-xs text-gray-500 text-center dark:text-gray-400">
                  By continuing, you agree to AdMesh&apos;s{" "}
                  <Link href="/terms" className="underline hover:text-primary">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link
                    href="/privacy"
                    className="underline hover:text-primary"
                  >
                    Privacy Policy
                  </Link>
                  , and to receive periodic emails with updates.
                </p>
              </form>
            ) : (
              // Sign Up Form
              <form onSubmit={handleSignUp} className="space-y-6">
                {selectedRole === "brand" ? (
                  <>
                    <div>
                      <label
                        htmlFor="website"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                      >
                        Company Website
                      </label>
                      <div className="relative">
                        <Input
                          id="website"
                          type="url"
                          placeholder="https://example.com"
                          value={website}
                          onChange={(e) => handleWebsiteChange(e.target.value)}
                          className="pl-10 h-12"
                          required
                        />
                        <Globe className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      </div>
                    </div>

                    {websiteDomain && (
                      <div>
                        <label
                          htmlFor="email-username"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          Work Email
                        </label>
                        <div className="flex">
                          <div className="relative flex-1">
                            <Input
                              id="email-username"
                              placeholder="username"
                              value={emailUsername}
                              onChange={(e) =>
                                handleEmailUsernameChange(e.target.value)
                              }
                              className="rounded-r-none h-12"
                              required
                            />
                            <span className="absolute right-3 top-3.5 text-gray-500">
                              @{websiteDomain}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      {selectedRole === "agent"
                        ? "Company Name"
                        : "Full Name"}
                    </label>
                    <div className="relative">
                      <Input
                        id="name"
                        placeholder={
                          selectedRole === "agent" ? "My Company" : "John Doe"
                        }
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="pl-10 h-12"
                        required
                      />
                      <User className="absolute left-3 top-3.5 text-gray-400" />
                    </div>
                  </div>
                )}

                {selectedRole !== "brand" && (
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      Email
                    </label>
                    <div className="relative">
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10 h-12"
                        required
                      />
                      <Mail className="absolute left-3 top-3.5 text-gray-400" />
                    </div>
                  </div>
                )}

                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="pl-10 h-12 pr-10"
                      required
                    />
                    <Lock className="absolute left-3 top-3.5 text-gray-400" />
                    <button
                      type="button"
                      className="absolute right-3 top-3.5 text-gray-400 hover:text-gray-500"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff /> : <Eye />}
                    </button>
                  </div>
                </div>

                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg p-3 text-sm">
                    {error}
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full h-12"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isCheckingWebsite ? "Validating website..." :
                       isCheckingAgentName ? "Checking agent name..." :
                       "Creating account..."}
                    </>
                  ) : (
                    "Create account"
                  )}
                </Button>

                <p className="mt-2 text-xs text-gray-500 text-center dark:text-gray-400">
                  By continuing, you agree to AdMesh&apos;s{" "}
                  <Link href="/terms" className="underline hover:text-primary">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link
                    href="/privacy"
                    className="underline hover:text-primary"
                  >
                    Privacy Policy
                  </Link>
                  , and to receive periodic emails with updates.
                </p>
              </form>
            )}

            <p className="text-center text-sm text-gray-600 dark:text-gray-400">
              {isSignUp ? "Already have an account?" : "Don't have an account?"}{" "}
              <button
                type="button"
                onClick={isSignUp ? handleSignInClick : handleGetStarted}
                className="text-blue-600 hover:text-blue-500 hover:underline font-medium"
              >
                {isSignUp ? "Sign in" : "Get started"}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
