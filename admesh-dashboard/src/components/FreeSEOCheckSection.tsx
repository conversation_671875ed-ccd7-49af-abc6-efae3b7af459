"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useAuth } from "@/hooks/use-auth";
import {
  Calendar,
  BarChart3,
  Eye,
  Download,
  RefreshCw,
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  Sparkles,
  ArrowRight,
  Plus
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface GEOHistoryItem {
  id: string;
  created_at: string;
  overall_score: number;
  website_analyzed: string;
  brand_name: string;
  analyzed_pages_count: number;
  total_queries_simulated: number;
  prompt_mention_rate: number;
  citation_rate: number;
  website_optimization: number;
  sentiment_tone: number;
  analysis_version: string;
}

export default function FreeSEOCheckSection() {
  const { user } = useAuth();
  const { ref: sectionRef, inView: sectionInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [reports, setReports] = useState<GEOHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const reportsPerPage = 5;

  const handleNewReport = () => {
    if (user) {
      // User is logged in, redirect to GEO report page
      window.location.href = "/dashboard/brand/geo-check";
    } else {
      // User is not logged in, redirect to sign-in with brand role
      window.location.href = "/auth/signin?role=brand&redirect=/dashboard/brand/geo-check";
    }
  };

  // Fetch reports from API
  const fetchReports = async (page: number = 1) => {
    if (!user) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/geo-reports?page=${page}&limit=${reportsPerPage}`, {
        headers: {
          'Authorization': `Bearer ${await user.getIdToken()}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (page === 1) {
          setReports(data.reports || []);
        } else {
          setReports(prev => [...prev, ...(data.reports || [])]);
        }
        setHasMore(data.hasMore || false);
      }
    } catch (error) {
      console.error('Error fetching reports:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load initial reports when user is available
  useEffect(() => {
    if (user) {
      fetchReports(1);
    }
  }, [user]);

  const handleLoadMore = () => {
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    fetchReports(nextPage);
  };

  const handleViewReport = (analysisId: string) => {
    window.location.href = `/dashboard/brand/geo-check/${analysisId}`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    return "Needs Improvement";
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      relative: formatDistanceToNow(date, { addSuffix: true }),
      absolute: date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };



  return (
    <section
      id="report-history"
      ref={sectionRef}
      className="w-full py-20 bg-gray-50 dark:bg-gray-900"
    >
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full text-sm font-medium">
            <BarChart3 className="w-4 h-4" />
            Report History
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-black dark:text-white mb-6">
            GEO Report History
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            Track your GEO performance over time with detailed analysis reports and actionable recommendations.
          </p>
        </motion.div>

        {/* New Report Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-center mb-12"
        >
          <Button
            onClick={handleNewReport}
            size="lg"
            className="bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium shadow-md hover:shadow-lg transition-all hover:-translate-y-1 duration-300"
          >
            <div className="flex items-center gap-2">
              <Plus className="w-5 h-5" />
              {user ? "Generate New Report" : "Get Free GEO Report"}
              <ArrowRight className="w-5 h-5" />
            </div>
          </Button>
        </motion.div>

        {/* Reports List */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {user ? (
            <>
              {loading && reports.length === 0 ? (
                <Card>
                  <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Loading analysis history...</p>
                    </div>
                  </CardContent>
                </Card>
              ) : reports.length === 0 ? (
                <Card>
                  <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Analysis History</h3>
                      <p className="text-muted-foreground text-center max-w-md">
                        You haven&apos;t run any GEO analyses yet. Generate your first report to start tracking your GEO performance over time.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Analysis History</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {reports.length} report{reports.length !== 1 ? 's' : ''} found • Track your GEO performance over time
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {reports.map((item, index) => {
                      const dateInfo = formatDate(item.created_at);
                      const isLatest = index === 0;

                      return (
                        <Card
                          key={item.id}
                          className={`transition-all duration-200 hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600 ${
                            isLatest ? 'border-blue-200 bg-blue-50/30 dark:bg-blue-950/20 dark:border-blue-800' : 'border-gray-200 dark:border-gray-700'
                          }`}
                        >
                          <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                              <div className="flex-1 space-y-2">
                                {/* Header with date */}
                                <div className="flex items-center gap-3">
                                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                    <Calendar className="h-4 w-4" />
                                    <span title={dateInfo.absolute}>{dateInfo.relative}</span>
                                  </div>

                                  {isLatest && (
                                    <Badge variant="outline" className="text-xs">
                                      Latest
                                    </Badge>
                                  )}
                                </div>

                                {/* Score and metrics */}
                                <div className="flex items-center gap-4">
                                  <div className="flex items-center gap-2">
                                    <div className="text-2xl font-bold">
                                      <span className={getScoreColor(item.overall_score)}>
                                        {item.overall_score}
                                      </span>
                                      <span className="text-muted-foreground text-sm">/100</span>
                                    </div>
                                    <Badge variant={getScoreBadgeVariant(item.overall_score)} className="text-xs">
                                      {getScoreLabel(item.overall_score)}
                                    </Badge>
                                  </div>

                                  <div className="flex-1 max-w-xs">
                                    <Progress value={item.overall_score} className="h-2" />
                                  </div>
                                </div>

                                {/* Quick metrics */}
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                                  <div className="flex flex-col">
                                    <span className="text-muted-foreground">Prompt Mentions</span>
                                    <span className="font-medium">{Math.round(item.prompt_mention_rate)}%</span>
                                  </div>
                                  <div className="flex flex-col">
                                    <span className="text-muted-foreground">Citations</span>
                                    <span className="font-medium">{Math.round(item.citation_rate)}%</span>
                                  </div>
                                  <div className="flex flex-col">
                                    <span className="text-muted-foreground">Pages Analyzed</span>
                                    <span className="font-medium">{item.analyzed_pages_count}</span>
                                  </div>
                                  <div className="flex flex-col">
                                    <span className="text-muted-foreground">Queries Tested</span>
                                    <span className="font-medium">{item.total_queries_simulated}</span>
                                  </div>
                                </div>

                                {/* Website info */}
                                <div className="text-xs text-muted-foreground">
                                  <span>Website: </span>
                                  <span className="font-mono">{item.website_analyzed}</span>
                                </div>
                              </div>

                              {/* Actions */}
                              <div className="flex flex-col gap-2 ml-4">
                                <Button
                                  onClick={() => handleViewReport(item.id)}
                                  variant="outline"
                                  size="sm"
                                  className="gap-2"
                                >
                                  <Eye className="h-3 w-3" />
                                  View Report
                                </Button>

                                <Button
                                  onClick={() => {
                                    // TODO: Implement export functionality
                                    console.log('Export report:', item.id);
                                  }}
                                  variant="ghost"
                                  size="sm"
                                  className="gap-2 text-muted-foreground"
                                >
                                  <Download className="h-3 w-3" />
                                  Export
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>

                  {/* Load More Button */}
                  {hasMore && (
                    <div className="flex justify-center pt-4">
                      <Button
                        onClick={handleLoadMore}
                        variant="outline"
                        disabled={loading}
                        className="gap-2"
                      >
                        {loading ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <Clock className="h-4 w-4" />
                        )}
                        {loading ? "Loading..." : "Load More Reports"}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Sign In to View Reports</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-4">
                    Sign in to your account to view your GEO analysis history and track your performance over time.
                  </p>
                  <Button
                    onClick={handleNewReport}
                    variant="outline"
                    className="gap-2"
                  >
                    <ArrowRight className="h-4 w-4" />
                    Sign In
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </motion.div>
      </div>
    </section>
  );
}
